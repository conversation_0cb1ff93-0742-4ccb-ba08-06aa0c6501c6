﻿using Microsoft.AspNetCore.Http;
using VNR.Core.Models;

namespace VNR.Core.Application.Command
{
    public abstract class CommandHandler<TRequest, TResult> : BaseApplicationService, ICommandHandler<TRequest, TResult>
        where TRequest : ICommand<TResult>
        where TResult : notnull
    {
        public CommandHandler(IHttpContextAccessor httpContextAccessor)
        : base(httpContextAccessor) { }

        //public CommandHandler(IHttpContextAccessor httpContextAccessor, ILoggingService loggingService)
        //: base(httpContextAccessor)
        //    => (_loggingService) = (loggingService);

        public abstract Task<IApiResult<TResult>> Handle(TRequest request, CancellationToken cancellationToken);
    }
}