﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Result Detail Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalResultDetail]", Description = "APIs chi tiết kết quả mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalResultDetailController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalResultDetailController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON><PERSON> danh sách Eva Goal Result Details
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Goal Result Details</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalResultDetailQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Goal Result Detail
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Result Detail</param>
    /// <returns>Id của Eva Goal Result Detail mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalResultDetailCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Goal Result Detail theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalResultDetailByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Result Detail
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalResultDetailCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Result Detail
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalResultDetailCommand command)
        => await HandleRequest(command);
}