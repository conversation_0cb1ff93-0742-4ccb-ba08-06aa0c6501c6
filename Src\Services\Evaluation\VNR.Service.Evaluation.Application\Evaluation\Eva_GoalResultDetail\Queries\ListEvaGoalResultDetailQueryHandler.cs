using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalResultDetail.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Queries;

public class ListEvaGoalResultDetailQueryHandler : QueryListHandler<ListEvaGoalResultDetailQuery, ListEvaGoalResultDetailDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultDetailService _service;

    public ListEvaGoalResultDetailQueryHandler(IEva_GoalResultDetailService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaGoalResultDetailDto>>> Handle(
        ListEvaGoalResultDetailQuery request, CancellationToken cancellationToken)
    {
        var listData = await _service.GetAllAsync();
        var result = _context.Mapper.Map<IEnumerable<ListEvaGoalResultDetailDto>>(listData);

        await _context.LoggingService.LogInformationAsync($"Retrieved {listData.Count()} Eva_GoalResultDetail records");

        return ResultKendoGrid(request.Request, result);
    }
}