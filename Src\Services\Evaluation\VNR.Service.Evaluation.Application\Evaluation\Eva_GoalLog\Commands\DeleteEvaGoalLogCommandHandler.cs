using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Commands;

public class DeleteEvaGoalLogCommandHandler : CommandHandler<DeleteEvaGoalLogCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalLogService _service;

    public DeleteEvaGoalLogCommandHandler(IEva_GoalLogService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaGoalLogCommand request,
        CancellationToken cancellationToken)
    {
        var result = await _service.DeleteAsync(request.ID);
        
        if (result)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully deleted Eva_GoalLog: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalLog not found for deletion: {request.ID}");
        }

        return Success(result, result ? "Delete Eva_GoalLog Successful" : "Eva_GoalLog not found");
    }
}
