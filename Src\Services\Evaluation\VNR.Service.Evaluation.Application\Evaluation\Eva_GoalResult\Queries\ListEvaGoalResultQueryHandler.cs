using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalResult.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Queries;

public class ListEvaGoalResultQueryHandler : QueryListHandler<ListEvaGoalResultQuery, ListEvaGoalResultDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultService _service;

    public ListEvaGoalResultQueryHandler(IEva_GoalResultService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaGoalResultDto>>> Handle(ListEvaGoalResultQuery request,
        CancellationToken cancellationToken)
    {
        var listData = await _service.GetAllAsync();
        var result = _context.Mapper.Map<IEnumerable<ListEvaGoalResultDto>>(listData);
        
        await _context.LoggingService.LogInformationAsync($"Retrieved {listData.Count()} Eva_GoalResult records");
        
        return ResultKendoGrid(request.Request, result);
    }
}
