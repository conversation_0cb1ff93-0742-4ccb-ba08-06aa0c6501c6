using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Category;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;

namespace VNR.Service.Evaluation.Application.Category.Commands;

public class CreateMeasurementUnitCommandHandler : CommandHandler<CreateMeasurementUnitCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly ICat_MeasurementUnitService _measurementUnitService;

    public CreateMeasurementUnitCommandHandler(ICat_MeasurementUnitService measurementUnitService,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _measurementUnitService) = (applicationContext, measurementUnitService);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateMeasurementUnitCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Cat_MeasurementUnit>(request.Request);
        var id = await _measurementUnitService.CreateAsync(entity);
        return Result(id);
    }
}