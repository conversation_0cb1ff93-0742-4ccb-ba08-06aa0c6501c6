using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinSchedule.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Queries;

public class GetEvaCheckinScheduleByIdQueryHandler : QueryHandler<GetEvaCheckinScheduleByIdQuery, EvaCheckinScheduleDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinScheduleService _service;

    public GetEvaCheckinScheduleByIdQueryHandler(IEva_CheckinScheduleService service,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<EvaCheckinScheduleDto>> Handle(GetEvaCheckinScheduleByIdQuery query,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(query.ID);
        var dto = _applicationContext.Mapper.Map<EvaCheckinScheduleDto>(entity);
        return Result(dto);
    }
}