﻿using Microsoft.AspNetCore.Http;
using VNR.Core.Models;
using VNR.Core.Models.Base;

namespace VNR.Core.Application.Query
{
    public abstract class QueryHandler<TQuery, TResult> : BaseApplicationService, IQueryHandler<TQuery, TResult>
        where TResult : BaseDto
        where TQuery : IQuery<TResult>
    {
        public QueryHandler(IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        { }

        public abstract Task<IApiResult<TResult>> Handle(TQuery request, CancellationToken cancellationToken);
    }
}