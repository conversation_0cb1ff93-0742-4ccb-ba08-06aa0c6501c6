using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;
using VNR.Service.Evaluation.Models.Category.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Category.Queries;

public class ListGoalPeriodQueryHandler : QueryListHandler<ListGoalPeriodQuery, ListGoalPeriodDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly ICat_GoalPeriodService _goalPeriodService;

    public ListGoalPeriodQueryHandler(ICat_GoalPeriodService goalPeriodService, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _goalPeriodService) = (applicationContext, goalPeriodService);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListGoalPeriodDto>>> Handle(ListGoalPeriodQuery query,
        CancellationToken cancellationToken)
    {
        var result = await _goalPeriodService.GetAllAsync();
        var finalResult = _applicationContext.Mapper.Map<IEnumerable<ListGoalPeriodDto>>(result);
        await _applicationContext.LoggingService.LogInformationAsync($"Retrieved {result.Count()} GoalPeriod records");
        return ResultKendoGrid(query.Request, finalResult);
    }
}