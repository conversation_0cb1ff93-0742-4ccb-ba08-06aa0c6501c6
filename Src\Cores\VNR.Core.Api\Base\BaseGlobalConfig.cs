﻿using System.Net;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;

namespace VNR.Core.Api.ApiConfigurations
{
    public static class BaseGlobalConfig
    {
        public static void Config()
        {
            //Config SecurityProtocol (Tls & https)
            ConfigSecurityProtocol();
        }

        /// <summary>
        /// Config SecurityProtocol (Tls & https)
        /// </summary>
        public static void ConfigSecurityProtocol()
        {
            ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12 | SecurityProtocolType.Tls11;

            ServicePointManager.ServerCertificateValidationCallback +=
                (object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors) => true;
        }
    }
}