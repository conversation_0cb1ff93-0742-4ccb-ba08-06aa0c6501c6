using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Category.Commands;
using VNR.Service.Evaluation.Application.Category.Queries;
namespace VNR.Service.Evaluation.Api.Controllers.Category;

/// <summary>
/// HrmTest Controller using Dapper
/// </summary>
[OpenApiTag("[Evaluation][Category][Cat_GoalGroup]", Description = "APIs danh mục nhóm mục tiêu module đánh giá")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Cat_GoalGroupController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Cat_GoalGroupController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// Lấy danh sách Goal Groups
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Goal Groups</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListGoalGroupQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới HrmTest
    /// </summary>
    /// <param name="command">Thông tin Goal Groups</param>
    /// <returns>Id của Goal Group mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateGoalGroupCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Goal Group theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetGoalGroupByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Goal Group
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateGoalGroupCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Goal Group
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteGoalGroupCommand command)
        => await HandleRequest(command);
}