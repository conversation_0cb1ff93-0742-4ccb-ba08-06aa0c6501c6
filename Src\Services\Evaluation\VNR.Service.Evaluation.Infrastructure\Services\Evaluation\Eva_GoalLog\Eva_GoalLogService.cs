using Microsoft.EntityFrameworkCore;
using EvaGoalLogEntity = VNR.Core.Domain.Entities.Evaluation.Eva_GoalLog;
using VNR.Core.Domain.Repository;
using VNR.Core.Common.Logging;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalLog;

public class Eva_GoalLogService : IEva_GoalLogService
{
    private readonly IGenericRepository<EvaGoalLogEntity, Guid> _repository;
    private readonly ILoggingService _loggingService;

    public Eva_GoalLogService(IGenericRepository<EvaGoalLogEntity, Guid> repository, ILoggingService loggingService)
    {
        _repository = repository;
        _loggingService = loggingService;
    }

    public async Task<IEnumerable<EvaGoalLogEntity>> GetAllAsync()
    {
        await _loggingService.LogInformationAsync("Getting all Eva_GoalLog entities");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<EvaGoalLogEntity?> GetByIdAsync(Guid id)
    {
        await _loggingService.LogInformationAsync($"Getting Eva_GoalLog by id {id}");
        return await _repository.GetByIdAsync(id);
    }

    public async Task<Guid> CreateAsync(EvaGoalLogEntity entity)
    {
        await _repository.AddAsync(entity);
        await _loggingService.LogInformationAsync($"Created Eva_GoalLog with id {entity.Id}");
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(EvaGoalLogEntity entity)
    {
        await _repository.UpdateAsync(entity);
        await _loggingService.LogInformationAsync($"Updated Eva_GoalLog with id {entity.Id}");
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _repository.DeleteAsync(id);
        await _loggingService.LogInformationAsync($"Deleted Eva_GoalLog with id {id}");
        return result;
    }
}
