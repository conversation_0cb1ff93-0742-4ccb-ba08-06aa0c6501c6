﻿using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterCors(this IServiceCollection services,
            IConfiguration configuration)
        {
            var allowedOrigins = configuration["AppSettings:AllowOrigin"];
            string[] origins = string.IsNullOrWhiteSpace(allowedOrigins)
                                    ? Array.Empty<string>()
                                    : allowedOrigins.Split(';', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries);

            // Add CORS services
            services.AddCors(options =>
            {
                options.AddPolicy("AllowOrigins", policy =>
                {
                    if (!string.IsNullOrWhiteSpace(allowedOrigins))
                    {
                        policy.WithOrigins(origins)
                            .AllowAnyHeader()
                            .AllowAnyMethod()
                            .AllowCredentials();
                    }
                    else
                    {
                        policy.AllowAnyOrigin()
                              .AllowAnyHeader()
                              .AllowAnyMethod();
                    }
                });
            });

            return services;
        }
    }
}