using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Commands;

public class CreateEvaCheckinGoalCommandHandler : CommandHandler<CreateEvaCheckinGoalCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinGoalService _service;

    public CreateEvaCheckinGoalCommandHandler(IEva_CheckinGoalService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaCheckinGoalCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Core.Domain.Entities.Evaluation.Eva_CheckinGoal>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}