﻿using HealthChecks.UI.Client;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics.HealthChecks;
using Microsoft.AspNetCore.Routing;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static void MapHealthChecksWithUI(this IEndpointRouteBuilder endpoints)
        {
            endpoints.MapHealthChecks("/health", new HealthCheckOptions
            {
                ResponseWriter = UIResponseWriter.WriteHealthCheckUIResponse,
            })
            .AllowAnonymous();

            //endpoints.MapHealthChecksUI(setupOptions: setup =>
            //{
            //    setup.UIPath = "/health-ui";
            //    setup.ApiPath = "/health-ui-api";
            //})
            //.AllowAnonymous();
        }
    }
}