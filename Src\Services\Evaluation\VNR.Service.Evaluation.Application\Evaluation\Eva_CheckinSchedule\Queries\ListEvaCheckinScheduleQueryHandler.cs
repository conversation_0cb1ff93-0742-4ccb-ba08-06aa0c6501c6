using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinSchedule.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Queries;

public class ListEvaCheckinScheduleQueryHandler : QueryListHandler<ListEvaCheckinScheduleQuery, ListEvaCheckinScheduleDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinScheduleService _service;

    public ListEvaCheckinScheduleQueryHandler(IEva_CheckinScheduleService service,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaCheckinScheduleDto>>> Handle(
        ListEvaCheckinScheduleQuery query, CancellationToken cancellationToken)
    {
        var result = await _service.GetAllAsync();
        var finalResult = _applicationContext.Mapper.Map<IEnumerable<ListEvaCheckinScheduleDto>>(result);
        await _applicationContext.LoggingService.LogInformationAsync(
            $"Retrieved {result.Count()} Eva_CheckinSchedule records");
        return ResultKendoGrid(query.Request, finalResult);
    }
}