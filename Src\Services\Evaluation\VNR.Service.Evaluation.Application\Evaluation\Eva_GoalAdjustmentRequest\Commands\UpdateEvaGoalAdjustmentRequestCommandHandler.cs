using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Commands;

public class
    UpdateEvaGoalAdjustmentRequestCommandHandler : CommandHandler<UpdateEvaGoalAdjustmentRequestCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_GoalAdjustmentRequestService _service;

    public UpdateEvaGoalAdjustmentRequestCommandHandler(IEva_GoalAdjustmentRequestService service,
        IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(
        UpdateEvaGoalAdjustmentRequestCommand request, CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _applicationContext.LoggingService.LogWarningAsync($"Eva_GoalAdjustmentRequest not found for update: {request.ID}");
            return Result(false);
        }

        _applicationContext.Mapper.Map(request.Request, entity);
        var res = await _service.UpdateAsync(entity);
        return Result(res);
    }
}