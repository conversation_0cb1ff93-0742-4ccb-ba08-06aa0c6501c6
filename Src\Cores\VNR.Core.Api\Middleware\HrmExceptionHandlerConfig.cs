﻿using System;
using System.Net;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using VNR.Core.Common.Logging;

namespace VNR.Core.Api.Middleware
{
    public static class HrmExceptionHandlerConfig
    {
        public static void ConfigService(IServiceCollection services)
        {
            services.AddScoped<GlobalExceptionFilterAttribute>();
            // Register GlobalExceptionMiddleware as transient service
            services.AddTransient<GlobalExceptionMiddleware>();
        }

        public static void ExceptionHandler(this IApplicationBuilder app, IWebHostEnvironment environment, ILoggerFactory loggerFactory)
        {
            // Get ILoggingService from DI container
            var loggingService = app.ApplicationServices.GetRequiredService<ILoggingService>();
            
            // Use middleware with proper constructor parameters
            app.UseMiddleware<GlobalExceptionMiddleware>();

            app.UseExceptionHandler(errorApp =>
            {
                errorApp.Run(async context =>
                {
                    var exceptionHandlerFeature = context.Features.Get<IExceptionHandlerFeature>();
                    var exception = exceptionHandlerFeature?.Error;

                    if (exception == null)
                        return;

                    // Log error using unified logging service
                    try
                    {
                        await loggingService.LogErrorAsync("Unhandled exception in UseExceptionHandler", exception, context, context.TraceIdentifier);
                    }
                    catch (Exception logEx)
                    {
                        // Fallback logging if logging service fails
                        Console.WriteLine($"Error logging exception: {logEx.Message}");
                    }

                    var response = new ExceptionResponse
                    {
                        StatusCode = (int)HttpStatusCode.InternalServerError,
                        Message = "An error occurred",
                        Details = GetErrorDetails(exception, environment)
                    };

                    context.Response.ContentType = "application/json";
                    context.Response.StatusCode = response.StatusCode;

                    var jsonResponse = JsonConvert.SerializeObject(response, Formatting.Indented, new StringEnumConverter());
                    await context.Response.WriteAsync(jsonResponse);
                });
            });
        }

        private static object GetErrorDetails(Exception ex, IWebHostEnvironment environment)
        {
            bool isRequestFromLocal = environment.IsDevelopment();

            return isRequestFromLocal
                ? new { ErrorRoot = ex.GetBaseException().ToString(), ErrorDetails = ex.ToString() }
                : new { ErrorRoot = "Internal Server Error", ErrorDetails = "An error occurred" };
        }
    }

    public class ExceptionResponse
    {
        public int StatusCode { get; set; }
        public string Message { get; set; }
        public object Details { get; set; }
    }
}