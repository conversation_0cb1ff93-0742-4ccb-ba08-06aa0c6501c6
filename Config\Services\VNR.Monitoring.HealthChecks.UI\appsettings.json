{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "HealthChecksUI": {"HealthChecks": [{"Name": "VNR.Service.Sample.Api", "Uri": "https://localhost:7068/health"}, {"Name": "VNR.Service.Shared.Api", "Uri": "https://localhost:7069/health"}, {"Name": "VNR.Service.IdentityServer.Identity", "Uri": "https://localhost:44310/health"}], "Webhooks": [{"Name": "", "Uri": "", "Payload": "", "RestoredPayload": ""}], "EvaluationTimeInSeconds": 60, "MinimumSecondsBetweenFailureNotifications": 60}}