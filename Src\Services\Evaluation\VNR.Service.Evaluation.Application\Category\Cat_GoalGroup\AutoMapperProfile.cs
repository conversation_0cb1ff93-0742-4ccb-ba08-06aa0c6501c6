using AutoMapper;
using VNR.Core.Domain.Entities.Category;
using VNR.Service.Evaluation.Models.Category.RequestDtos;
using VNR.Service.Evaluation.Models.Category.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Category;

public class GoalGroupAutoMapperProfile : Profile
{
    public GoalGroupAutoMapperProfile()
    {
        CreateMap<Cat_GoalGroup, Cat_GoalGroupDto>()
            .ForMember(dest => dest.ID, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description));

        CreateMap<Cat_GoalGroup, ListGoalGroupDto>()
            .ForMember(dest => dest.ID, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.Code, opt => opt.MapFrom(src => src.Code))
            .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
            .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description));

        CreateMap<CreateGoalGroupCommandRequest, Cat_GoalGroup>();
        CreateMap<UpdateGoalGroupCommandRequest, Cat_GoalGroup>();
    }
}
