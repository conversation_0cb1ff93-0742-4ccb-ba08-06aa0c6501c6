using Microsoft.EntityFrameworkCore;
using VNR.Core.Common.Logging;
using VNR.Core.Domain.Entities.Category;
using VNR.Core.Domain.Entities.Test;
using VNR.Core.Domain.Repository;
using VNR.Infrastructure.BaseRepositories;
using VNR.Service.Evaluation.Application.Category.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services
{
    public class Cat_GoalGroupService : ICat_GoalGroupService
    {
        private readonly IGenericRepository<Cat_GoalGroup, Guid> _repository;
        private readonly ILoggingService _loggingService;
        //private readonly IUnitOfWork _unitOfWork;

        public Cat_GoalGroupService(IGenericRepository<Cat_GoalGroup, Guid> repository, ILoggingService loggingService)
        {
            _repository = repository;
            _loggingService = loggingService;
            //_unitOfWork = repository.unitofwork;
        }

        public async Task<IEnumerable<Cat_GoalGroup>> GetAllAsync()
        {
            await _loggingService.LogInformationAsync("Getting all Cat_GoalGroup entities with Entity Framework");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }
        public async Task<Guid> CreateAsync(Cat_GoalGroup entity)
        {
            await _repository.AddAsync(entity);

            //_unitOfWork.SaveChanges();

            return entity.Id;
        }

        public async Task<Cat_GoalGroup?> GetByIdAsync(Guid id)
        {
            await _loggingService.LogInformationAsync($"Getting Cat_GoalGroup by id {id}");
            return await _repository.GetByIdAsync(id);
        }

        public async Task<bool> UpdateAsync(Cat_GoalGroup entity)
        {
            await _repository.UpdateAsync(entity);
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
            => await _repository.DeleteAsync(id);
    }
}