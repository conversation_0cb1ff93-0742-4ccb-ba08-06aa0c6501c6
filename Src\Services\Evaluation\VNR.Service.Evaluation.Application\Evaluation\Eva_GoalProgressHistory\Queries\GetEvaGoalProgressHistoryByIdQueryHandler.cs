using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalProgressHistory.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Queries;

public class GetEvaGoalProgressHistoryByIdQueryHandler : QueryHandler<GetEvaGoalProgressHistoryByIdQuery, EvaGoalProgressHistoryDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalProgressHistoryService _service;

    public GetEvaGoalProgressHistoryByIdQueryHandler(IEva_GoalProgressHistoryService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<EvaGoalProgressHistoryDto>> Handle(GetEvaGoalProgressHistoryByIdQuery request,
        CancellationToken cancellationToken)
    {
        await _context.LoggingService.LogInformationAsync($"Getting Eva_GoalProgressHistory by ID: {request.ID}");

        var entity = await _service.GetByIdAsync(request.ID);

        if (entity != null)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully retrieved Eva_GoalProgressHistory: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalProgressHistory not found: {request.ID}");
        }

        var result = _context.Mapper.Map<EvaGoalProgressHistoryDto>(entity);
        return Result(result);
    }
}
