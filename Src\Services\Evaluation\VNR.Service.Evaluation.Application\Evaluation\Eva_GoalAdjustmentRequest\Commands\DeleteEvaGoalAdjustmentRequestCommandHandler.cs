using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Commands;

public class DeleteEvaGoalAdjustmentRequestCommandHandler : CommandHandler<DeleteEvaGoalAdjustmentRequestCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_GoalAdjustmentRequestService _service;

    public DeleteEvaGoalAdjustmentRequestCommandHandler(IEva_GoalAdjustmentRequestService service,
        IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaGoalAdjustmentRequestCommand request,
        CancellationToken cancellationToken)
    {
        var res = await _service.DeleteAsync(request.ID);
        return Result(res);
    }
}