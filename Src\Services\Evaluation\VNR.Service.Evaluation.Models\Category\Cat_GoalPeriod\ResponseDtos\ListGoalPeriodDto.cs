using VNR.Core.Models.Base;

namespace VNR.Service.Evaluation.Models.Category.ResponseDtos;

public class ListGoalPeriodDto : BaseDto
{
    public string Code { get; set; }
    public string Name { get; set; }
    public string Description { get; set; }
    public string Type { get; set; }
    public DateTimeOffset? StartDate { get; set; }
    public DateTimeOffset? EndDate { get; set; }
    public Guid? ParentId { get; set; }
}
