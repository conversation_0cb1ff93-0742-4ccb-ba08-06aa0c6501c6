using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Hosting.CompositionRoot.Extensions
{
    public static class MediatRExtensions
    {
        public static IServiceCollection RegisterMediatR(this IServiceCollection services, List<Assembly> assemblies)
        {
            if (assemblies == null || assemblies.Count == 0)
                return services;

            //// Lấy tất cả các assembly trong domain hiện tại
            //var currentAssemblies = AppDomain.CurrentDomain.GetAssemblies().ToList();
            //assemblies.AddRange(currentAssemblies);

            // Lọc các assembly chứa handlers - thường là các Application hoặc Business projects
            var handlerAssemblies = assemblies
                .Where(a => a.GetName().Name.EndsWith(".Application"))
                .ToList();

            // Đăng ký tất cả các services MediatR từ tất cả các assembly đã chỉ định
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssemblies(handlerAssemblies.ToArray()));

            return services;
        }
    }
}