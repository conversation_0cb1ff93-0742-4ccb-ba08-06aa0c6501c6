﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Adjustment Request Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalAdjustmentRequest]", Description = "APIs yêu cầu điều chỉnh mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalAdjustmentRequestController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalAdjustmentRequestController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON><PERSON> danh sách Eva Goal Adjustment Requests
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Goal Adjustment Requests</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalAdjustmentRequestQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Goal Adjustment Request
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Adjustment Request</param>
    /// <returns>Id của Eva Goal Adjustment Request mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalAdjustmentRequestCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Goal Adjustment Request theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalAdjustmentRequestByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Adjustment Request
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalAdjustmentRequestCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Adjustment Request
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalAdjustmentRequestCommand command)
        => await HandleRequest(command);
}