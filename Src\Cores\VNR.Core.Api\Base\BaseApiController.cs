﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using NJsonSchema.NewtonsoftJson.Converters;
using VNR.Core.Api.Services;
using VNR.Core.Common.Logging;
using VNR.Core.Exceptions;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Core.Models.Enums;

namespace VNR.Core.Api
{
    [ApiController]
    public class BaseApiController : ControllerBase
    {
        protected readonly IMediator _mediator;
        protected readonly ILoggingService _loggingService;

        public BaseApiController(IApiContext apiContext)
        {
            _mediator = apiContext.Mediator;
            _loggingService = apiContext.LoggingService;
        }

        protected async Task<IActionResult> HandleRequest<T>(IRequest<T> request)
        {
            ApiResultStatus? statusResponse = null;

            var jsonSettings = new JsonSerializerSettings()
            {
                Formatting = Formatting.Indented,
                Converters = new List<JsonConverter>()
                {
                    new StringEnumConverter(),
                    new JsonReferenceConverter()
                }
            };

            try
            {
                //var result = await _auditLogInterceptor.InterceptAsync(request, () => _mediator.Send(request));
                var result = await _mediator.Send(request);
                return Ok(result);
            }
            catch (RequestArgumentsValidatorException ex)
            {
                statusResponse = ApiResultStatus.INVALID_REQUEST_VALIDATOR;

                // Log validation error using unified logging service
                await _loggingService.LogErrorAsync(
                    "Request validation failed",
                    ex,
                    HttpContext,
                    HttpContext.TraceIdentifier);

                var result = JsonConvert.SerializeObject(new ApiResult()
                {
                    Data = ((ArgumentsValidatorException)ex).Parameters,
                    Status = statusResponse.Value
                });
                return new ObjectResult(result);
            }
            catch (BusinessArgumentsValidatorException ex)
            {
                statusResponse = ApiResultStatus.INVALID_BUSINESS_VALIDATOR;

                // Log business validation error using unified logging service
                await _loggingService.LogErrorAsync(
                    "Business validation failed",
                    ex,
                    HttpContext,
                    HttpContext.TraceIdentifier);

                var result = JsonConvert.SerializeObject(new ApiResult()
                {
                    Data = ((ArgumentsValidatorException)ex).Parameters,
                    Status = statusResponse.Value
                });
                return new ObjectResult(result);
            }
            catch (ValidationException ex)
            {
                // Log validation error using unified logging service
                await _loggingService.LogErrorAsync(
                    "FluentValidation failed",
                    ex,
                    HttpContext,
                    HttpContext.TraceIdentifier);

                var resultObject = new ApiResult()
                {
                    Status = ApiResultStatus.BAD_REQUEST,
                    Data = ex.Errors.Select(x => x.ErrorMessage).ToArray(),
                    Message = "Parameters invalid"
                };
                return BadRequest(resultObject);
            }
            catch (Exception ex)
            {
                // Log general error using unified logging service
                await _loggingService.LogErrorAsync(
                    "Unexpected error occurred",
                    ex,
                    HttpContext,
                    HttpContext.TraceIdentifier);

                object resultObject = null;
                bool isRequestFromLocal = false;

#if DEBUG
                isRequestFromLocal = true;
#endif
                //Only show details error in local
                if (Equals(isRequestFromLocal, true))
                {
                    var rootException = ex.GetBaseException();

                    resultObject = new ApiResult()
                    {
                        Status = ApiResultStatus.EXCEPTION,
                        Data = new
                        {
                            ErrorRoot = rootException,
                            ErrorDetails = ex
                        },
                        Message = "An error occurred"
                    };
                }
                else
                {
                    resultObject = new ApiResult()
                    {
                        Status = ApiResultStatus.EXCEPTION,
                        Data = new
                        {
                            ErrorRoot = "Internal Server Error",
                            ErrorDetails = "An error occurred"
                        },
                        Message = "An error occurred"
                    };
                }

                var result = JsonConvert.SerializeObject(resultObject, jsonSettings);
                return new ObjectResult(result);
            }
        }

        public string AuthorizationHeader
        {
            get { return HttpContext?.Request?.Headers["Authorization"] ?? string.Empty; }
        }

        protected IApiResult ResultObject(object data, string message = null, ApiResultStatus status = ApiResultStatus.SUCCESS, ErrorCode code = ErrorCode.Success)
        {
            return new ApiResult()
            {
                Data = data,
                Status = status,
                Code = code,
                Message = !string.IsNullOrEmpty(message) ? message : code.ToString()
            };
        }

        protected IApiResult<T> Result<T>(T data, string message = null, ApiResultStatus status = ApiResultStatus.SUCCESS, ErrorCode code = ErrorCode.Success)
        {
            return new ApiResult<T>()
            {
                Data = data,
                Status = status,
                Code = code,
                Message = !string.IsNullOrEmpty(message) ? message : code.ToString()
            };
        }

        protected IApiResult<T> Result<T>(T data, ErrorCode code, string message = null, ApiResultStatus status = ApiResultStatus.SUCCESS)
        {
            return new ApiResult<T>()
            {
                Data = data,
                Status = status,
                Code = code,
                Message = !string.IsNullOrEmpty(message) ? message : code.ToString()
            };
        }

        protected IApiResult<BaseResponseGridModel<T>> ResultKendoGrid<T>(KendoGridRequestModel requestModel,
            IEnumerable<T> data, string message = null,
            ApiResultStatus status = ApiResultStatus.SUCCESS) where T : BaseDto
        {
            return Result(new BaseResponseGridModel<T>(requestModel, data), message, status);
        }
    }
}