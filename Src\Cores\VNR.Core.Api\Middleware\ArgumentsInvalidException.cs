﻿using System;
using System.Collections.Generic;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace VNR.Core.Api.Middleware
{
    public class ArgumentsValidatorException : Exception
    {
        [JsonObject(NamingStrategyType = typeof(CamelCaseNamingStrategy))]
        public class ParamDetails
        {
            public ParamDetails()
            {
            }

            public ParamDetails(string name, string description)
            {
                Name = name;
                Description = description;
            }

            public string Name { get; set; }
            public string Description { get; set; }
        }

        public List<ParamDetails> Parameters { get; set; } = new List<ParamDetails>();

        public ArgumentsValidatorException AddParamDetails(string name, string description)
        {
            Parameters.Add(new ParamDetails(name, description));
            return this;
        }
    }
}