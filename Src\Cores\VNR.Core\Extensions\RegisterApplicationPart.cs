﻿using System.Collections.Generic;
using System.Reflection;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.DependencyInjection;
using System.Linq;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterApplicationPart(this IServiceCollection services,
            List<Assembly> referencedAssemblies)
        {
            // Đăng ký controllers từ các assembly
            var mvcBuilder = services.AddControllers();

            // Thêm controllers từ assembly hiện tại
            mvcBuilder.AddApplicationPart(Assembly.GetExecutingAssembly());

            // Thêm controllers từ các module Shared
            foreach (var assembly in referencedAssemblies)
            {
                mvcBuilder.AddApplicationPart(assembly);
            }

            // Hoàn tất cấu hình MVC
            mvcBuilder.AddControllersAsServices();

            // Đăng ký tất cả các Controllers trong assemblies được chỉ định
            var controllerTypes = referencedAssemblies
                .SelectMany(assembly => assembly.GetExportedTypes())
                .Where(type => !type.IsAbstract && 
                              !type.IsInterface && 
                               type.Name.EndsWith("Controller") &&
                               typeof(Microsoft.AspNetCore.Mvc.ControllerBase).IsAssignableFrom(type))
                .ToList();

            // Đăng ký các controllers một cách tường minh
            foreach (var controllerType in controllerTypes)
            {
                services.AddTransient(controllerType);
            }

            return services;
        }
    }
}