﻿using VNR.Core.Api.Middleware;
using VNR.Core.Exceptions;
using VNR.Core.Models;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using NJsonSchema.Converters;
using NJsonSchema.NewtonsoftJson.Converters;
using System;
using System.Collections.Generic;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using VNR.Core.Models.Enums;
using VNR.Core.Common.Logging;

namespace VNR.Core.Api.Middleware
{
    public static class ExceptionHandlerConfig
    {
        private static Func<HttpContext, Exception, bool> ExceptionHandler;

        public static void SetExceptionHandler(IWebHostEnvironment environment)
            => ExceptionHandler = GetExceptionHandler(environment);

        public static void ConfigService(IServiceCollection services, IHttpContextAccessor httpContextAccessor, ILogger logger, ILoggingService loggingService)
        {
            // Register the IExceptionHandler implementations
            services.AddExceptionHandler<VnrGlobalExceptionHandler>();
            services.AddExceptionHandler<VnrExceptionHandler>();

            // Register the WebApi2ExceptionProvider
            services.AddSingleton<ModernWebApi2ExceptionProvider>();

            // Add the MVC filter for controller-based APIs
            services.Configure<MvcOptions>(options =>
            {
                options.Filters.Add(new GlobalExceptionFilterAttribute(httpContextAccessor, logger, loggingService));
            });
        }

        /// <summary>
        /// Config Global Exception Handler
        /// </summary>
        /// <param name="app"></param>
        public static void Config(IApplicationBuilder app)
        {
            app.UseExceptionHandler();
            app.UseMiddleware<GlobalExceptionMiddleware>();
        }

        public static Func<HttpContext, Exception, bool> GetExceptionHandler(IWebHostEnvironment environment)
        {
            var jsonSettings = new JsonSerializerSettings()
            {
                Formatting = Formatting.Indented,
                Converters = new List<JsonConverter>()
                {
                    new StringEnumConverter(),
                    new JsonReferenceConverter()
                }
            };

            return (context, exception) =>
            {
                if (exception.GetType().IsSubclassOf(typeof(ArgumentsValidatorException)))
                {
                    context.Response.StatusCode = (int)HttpStatusCode.OK;
                    context.Response.ContentType = "application/json";

                    ApiResultStatus? statusResponse = null;

                    if (exception.GetType() == typeof(RequestArgumentsValidatorException))
                        statusResponse = ApiResultStatus.INVALID_REQUEST_VALIDATOR;
                    else if (exception.GetType() == typeof(BusinessArgumentsValidatorException))
                        statusResponse = ApiResultStatus.INVALID_BUSINESS_VALIDATOR;
                    else
                        statusResponse = ApiResultStatus.FAIL;

                    var result = JsonConvert.SerializeObject(new ApiResult()
                    {
                        Data = ((ArgumentsValidatorException)exception).Parameters,
                        Status = statusResponse.Value
                    });

                    context.Response.WriteAsync(result);
                    return true;
                }
                else
                {
                    context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                    context.Response.ContentType = "application/json";

                    object resultObject = null;
                    bool isRequestFromLocal = environment.IsDevelopment() || environment.IsEnvironment("server.IsLocal");

#if DEBUG
                    isRequestFromLocal = true;
#endif
                    //Only show details error in local
                    if (Equals(isRequestFromLocal, true))
                    {
                        var rootException = exception.GetBaseException();

                        resultObject = new ApiResult()
                        {
                            Status = ApiResultStatus.EXCEPTION,
                            Data = new
                            {
                                ErrorRoot = rootException.ToString(),
                                ErrorDetails = exception.ToString()
                            },
                            Message = "An error occurred"
                        };
                    }
                    else
                    {
                        resultObject = new ApiResult()
                        {
                            Status = ApiResultStatus.EXCEPTION,
                            Data = new
                            {
                                ErrorRoot = "Internal Server Error",
                                ErrorDetails = "An error occurred"
                            },
                            Message = "An error occurred"
                        };
                    }

                    var result = JsonConvert.SerializeObject(resultObject, jsonSettings);
                    context.Response.WriteAsync(result);
                    return true;
                }
            };
        }
    }

    // Implement IExceptionHandler for .NET 8
    public class VnrGlobalExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<VnrGlobalExceptionHandler> _logger;
        private readonly IWebHostEnvironment _environment;
        private readonly ILoggingService _loggingService;

        public VnrGlobalExceptionHandler(ILogger<VnrGlobalExceptionHandler> logger, IWebHostEnvironment environment, ILoggingService loggingService)
        {
            _logger = logger;
            _environment = environment;
            _loggingService = loggingService;
        }

        public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
        {
            // Log error using unified logging service
            try
            {
                await _loggingService.LogErrorAsync("An unhandled exception occurred", exception, httpContext, httpContext.TraceIdentifier);
            }
            catch (Exception logEx)
            {
                // Fallback to ILogger if unified logging service fails
                _logger.LogError(exception, "An unhandled exception occurred");
                _logger.LogError(logEx, "Error logging exception with unified logging service");
            }
            
            var jsonSettings = new JsonSerializerSettings()
            {
                Formatting = Formatting.Indented,
                Converters = new List<JsonConverter>()
                {
                    new StringEnumConverter(),
                    new JsonReferenceConverter()
                }
            };

            if (exception.GetType().IsSubclassOf(typeof(ArgumentsValidatorException)))
            {
                httpContext.Response.StatusCode = (int)HttpStatusCode.OK;
                httpContext.Response.ContentType = "application/json";

                ApiResultStatus? statusResponse = null;

                if (exception.GetType() == typeof(RequestArgumentsValidatorException))
                    statusResponse = ApiResultStatus.INVALID_REQUEST_VALIDATOR;
                else if (exception.GetType() == typeof(BusinessArgumentsValidatorException))
                    statusResponse = ApiResultStatus.INVALID_BUSINESS_VALIDATOR;
                else
                    statusResponse = ApiResultStatus.FAIL;

                var result = JsonConvert.SerializeObject(new ApiResult()
                {
                    Data = ((ArgumentsValidatorException)exception).Parameters,
                    Status = statusResponse.Value
                });

                await httpContext.Response.WriteAsync(result, cancellationToken);
                return true;
            }
            else
            {
                httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                httpContext.Response.ContentType = "application/json";

                object resultObject = null;
                bool isRequestFromLocal = _environment.IsDevelopment() || _environment.IsEnvironment("server.IsLocal");

#if DEBUG
                isRequestFromLocal = true;
#endif
                //Only show details error in local
                if (Equals(isRequestFromLocal, true))
                {
                    var rootException = exception.GetBaseException();

                    resultObject = new ApiResult()
                    {
                        Status = ApiResultStatus.EXCEPTION,
                        Data = new
                        {
                            ErrorRoot = rootException.ToString(),
                            ErrorDetails = exception.ToString()
                        },
                        Message = "An error occurred"
                    };
                }
                else
                {
                    resultObject = new ApiResult()
                    {
                        Status = ApiResultStatus.EXCEPTION,
                        Data = new
                        {
                            ErrorRoot = "Internal Server Error",
                            ErrorDetails = "An error occurred"
                        },
                        Message = "An error occurred"
                    };
                }

                var result = JsonConvert.SerializeObject(resultObject, jsonSettings);
                await httpContext.Response.WriteAsync(result, cancellationToken);
                return true;
            }
        }
    }
}