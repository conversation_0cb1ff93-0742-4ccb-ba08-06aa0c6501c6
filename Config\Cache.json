{"Cache": {"MemoryCacheConfiguration": {"Enabled": true, "DefaultExpirationMinutes": 30, "SizeLimit": 104857600, "CompactionPercentage": 0.1}, "RedisCacheConfiguration": {"Enabled": true, "ConnectionString": "127.0.0.1:6380,syncTimeout=60000,responseTimeout=60000,connectTimeout=10000,allowAdmin=true,password=7Ri7zcOPdPG4thsgyncp7Q==", "InstanceName": "VNR:", "DefaultDatabase": 0, "DefaultExpirationMinutes": 60, "ConnectTimeout": 5000, "SyncTimeout": 5000, "AbortConnect": false}, "CacheDefaultConfiguration": {"DefaultExpirationMinutes": 30, "EnableCompression": true, "EnableLogging": true, "DefaultKeyPrefix": "VNR:"}}, "Logging": {"LogLevel": {"VNR.Infrastructure.BaseRepositories.Caching": "Debug"}}}