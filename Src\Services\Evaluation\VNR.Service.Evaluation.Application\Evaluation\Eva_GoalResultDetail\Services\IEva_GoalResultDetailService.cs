using EvaGoalResultDetailEntity = VNR.Core.Domain.Entities.Evaluation.Eva_GoalResultDetail;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Services
{
    public interface IEva_GoalResultDetailService
    {
        Task<IEnumerable<EvaGoalResultDetailEntity>> GetAllAsync();
        Task<EvaGoalResultDetailEntity?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(EvaGoalResultDetailEntity entity);
        Task<bool> UpdateAsync(EvaGoalResultDetailEntity entity);
        Task<bool> DeleteAsync(Guid id);
    }
}