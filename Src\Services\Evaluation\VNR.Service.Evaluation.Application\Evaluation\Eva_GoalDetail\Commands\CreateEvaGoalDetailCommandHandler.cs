using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalDetail.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalDetail.Commands;

public class CreateEvaGoalDetailCommandHandler : CommandHandler<CreateEvaGoalDetailCommand, Guid>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalDetailService _service;

    public CreateEvaGoalDetailCommandHandler(IEva_GoalDetailService service, IApplicationContext context) : base(
        context.Accessor)
    {
        (_service, _context) = (service, context);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalDetailCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _context.Mapper.Map<VNR.Core.Domain.Entities.Evaluation.Eva_GoalDetail>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}