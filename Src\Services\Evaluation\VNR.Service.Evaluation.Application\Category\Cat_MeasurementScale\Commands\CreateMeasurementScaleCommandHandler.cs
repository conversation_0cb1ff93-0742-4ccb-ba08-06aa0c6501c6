using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Category;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;

namespace VNR.Service.Evaluation.Application.Category.Commands;

public class CreateMeasurementScaleCommandHandler : CommandHandler<CreateMeasurementScaleCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly ICat_MeasurementScaleService _scaleService;

    public CreateMeasurementScaleCommandHandler(ICat_MeasurementScaleService scaleService,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _scaleService) = (applicationContext, scaleService);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateMeasurementScaleCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Cat_MeasurementScale>(request.Request);
        var id = await _scaleService.CreateAsync(entity);
        return Result(id);
    }
}