using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;

public interface IEva_GoalProgressHistoryService
{
    Task<IEnumerable<Core.Domain.Entities.Evaluation.Eva_GoalProgressHistory>> GetAllAsync();
    Task<Core.Domain.Entities.Evaluation.Eva_GoalProgressHistory?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(Core.Domain.Entities.Evaluation.Eva_GoalProgressHistory entity);
    Task<bool> UpdateAsync(Core.Domain.Entities.Evaluation.Eva_GoalProgressHistory entity);
    Task<bool> DeleteAsync(Guid id);
}
