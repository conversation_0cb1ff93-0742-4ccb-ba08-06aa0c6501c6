using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalLog.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Queries;

public class GetEvaGoalLogByIdQueryHandler : QueryHandler<GetEvaGoalLogByIdQuery, EvaGoalLogDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalLogService _service;

    public GetEvaGoalLogByIdQueryHandler(IEva_GoalLogService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<EvaGoalLogDto>> Handle(GetEvaGoalLogByIdQuery request,
        CancellationToken cancellationToken)
    {
        await _context.LoggingService.LogInformationAsync($"Getting Eva_GoalLog by ID: {request.ID}");

        var entity = await _service.GetByIdAsync(request.ID);

        if (entity != null)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully retrieved Eva_GoalLog: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalLog not found: {request.ID}");
        }

        var result = _context.Mapper.Map<EvaGoalLogDto>(entity);
        return Result(result);
    }
}
