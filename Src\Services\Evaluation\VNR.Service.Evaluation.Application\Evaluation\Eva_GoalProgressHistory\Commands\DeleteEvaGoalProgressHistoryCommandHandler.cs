using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Commands;

public class DeleteEvaGoalProgressHistoryCommandHandler : CommandHandler<DeleteEvaGoalProgressHistoryCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalProgressHistoryService _service;

    public DeleteEvaGoalProgressHistoryCommandHandler(IEva_GoalProgressHistoryService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaGoalProgressHistoryCommand request,
        CancellationToken cancellationToken)
    {
        var result = await _service.DeleteAsync(request.ID);
        
        if (result)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully deleted Eva_GoalProgressHistory: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalProgressHistory not found for deletion: {request.ID}");
        }

        return Success(result, result ? "Delete Eva_GoalProgressHistory Successful" : "Eva_GoalProgressHistory not found");
    }
}
