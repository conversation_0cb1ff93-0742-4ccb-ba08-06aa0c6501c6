using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalLog.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Queries;

public class ListEvaGoalLogQueryHandler : QueryListHandler<ListEvaGoalLogQuery, ListEvaGoalLogDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalLogService _service;

    public ListEvaGoalLogQueryHandler(IEva_GoalLogService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaGoalLogDto>>> Handle(ListEvaGoalLogQuery request,
        CancellationToken cancellationToken)
    {
        var listData = await _service.GetAllAsync();
        var result = _context.Mapper.Map<IEnumerable<ListEvaGoalLogDto>>(listData);
        
        await _context.LoggingService.LogInformationAsync($"Retrieved {listData.Count()} Eva_GoalLog records");
        
        return ResultKendoGrid(request.Request, result);
    }
}
