using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinGoal.RequestDtos;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinGoal.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Queries;

public class GetEvaCheckinGoalByIdQueryHandler : QueryHandler<GetEvaCheckinGoalByIdQuery, EvaCheckinGoalDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinGoalService _service;

    public GetEvaCheckinGoalByIdQueryHandler(IEva_CheckinGoalService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<EvaCheckinGoalDto>> Handle(GetEvaCheckinGoalByIdQuery request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        var dto = _applicationContext.Mapper.Map<EvaCheckinGoalDto>(entity);
        return Result(dto);
    }
}