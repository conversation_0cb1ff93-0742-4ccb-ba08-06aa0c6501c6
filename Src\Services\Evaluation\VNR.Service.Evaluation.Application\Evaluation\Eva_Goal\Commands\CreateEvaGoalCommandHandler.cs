using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Commands;

public class CreateEvaGoalCommandHandler : CommandHandler<CreateEvaGoalCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_GoalService _goalService;

    public CreateEvaGoalCommandHandler(IEva_GoalService goalService, IApplicationContext applicationContext) : base(
        applicationContext.Accessor)
    {
        (_applicationContext, _goalService) = (applicationContext, goalService);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Eva_Goal>(request.Request);
        var id = await _goalService.CreateAsync(entity);
        return Result(id);
    }
}