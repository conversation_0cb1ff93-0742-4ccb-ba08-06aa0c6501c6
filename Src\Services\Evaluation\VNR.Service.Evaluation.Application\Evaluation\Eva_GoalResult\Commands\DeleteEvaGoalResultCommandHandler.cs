using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Commands;

public class DeleteEvaGoalResultCommandHandler : CommandHandler<DeleteEvaGoalResultCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultService _service;

    public DeleteEvaGoalResultCommandHandler(IEva_GoalResultService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaGoalResultCommand request,
        CancellationToken cancellationToken)
    {
        var result = await _service.DeleteAsync(request.ID);
        
        if (result)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully deleted Eva_GoalResult: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResult not found for deletion: {request.ID}");
        }

        return Success(result, result ? "Delete Eva_GoalResult Successful" : "Eva_GoalResult not found");
    }
}
