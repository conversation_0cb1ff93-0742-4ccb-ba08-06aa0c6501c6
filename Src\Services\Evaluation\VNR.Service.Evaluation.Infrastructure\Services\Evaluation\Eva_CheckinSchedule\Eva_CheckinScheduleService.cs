using VNR.Core.Common.Logging;
using EvaCheckinScheduleEntity = VNR.Core.Domain.Entities.Evaluation.Eva_CheckinSchedule;
using VNR.Core.Domain.Repository;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;
using Microsoft.EntityFrameworkCore;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_CheckinSchedule
{
    public class Eva_CheckinScheduleService : IEva_CheckinScheduleService
    {
        private readonly IGenericRepository<EvaCheckinScheduleEntity, Guid> _repository;
        private readonly ILoggingService _loggingService;

        public Eva_CheckinScheduleService(IGenericRepository<EvaCheckinScheduleEntity, Guid> repository, ILoggingService loggingService)
        {
            _repository = repository;
            _loggingService = loggingService;
        }

        public async Task<IEnumerable<EvaCheckinScheduleEntity>> GetAllAsync()
        {
            await _loggingService.LogInformationAsync("Getting all Eva_CheckinSchedule entities");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }

        public async Task<EvaCheckinScheduleEntity?> GetByIdAsync(Guid id)
        {
            await _loggingService.LogInformationAsync($"Getting Eva_CheckinSchedule by id {id}");
            return await _repository.GetByIdAsync(id);
        }

        public async Task<Guid> CreateAsync(EvaCheckinScheduleEntity entity)
        {
            await _repository.AddAsync(entity);
            return entity.Id;
        }

        public async Task<bool> UpdateAsync(EvaCheckinScheduleEntity entity)
        {
            await _repository.UpdateAsync(entity);
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
            => await _repository.DeleteAsync(id);
    }
}