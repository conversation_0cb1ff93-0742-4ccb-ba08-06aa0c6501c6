using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Evaluation;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Commands;

public class UpdateEvaGoalResultCommandHandler : CommandHandler<UpdateEvaGoalResultCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultService _service;

    public UpdateEvaGoalResultCommandHandler(IEva_GoalResultService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaGoalResultCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResult not found for update: {request.ID}");
            return Result(false);
        }

        _context.Mapper.Map(request.Request, entity);
        var result = await _service.UpdateAsync(entity);
        return Result(result);
    }
}
