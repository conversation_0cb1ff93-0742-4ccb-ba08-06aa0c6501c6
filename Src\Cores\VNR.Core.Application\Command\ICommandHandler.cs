﻿using MediatR;
using VNR.Core.Application.Models;
using VNR.Core.Models;

namespace VNR.Core.Application.Command
{
    public interface ICommandHandler<in TCommand> : IRequestHandler<TCommand>
        where TCommand : ICommand
    {
    }

    public interface ICommandHandler<in TCommand, TResult> : IRequestHandler<TCommand, IApiResult<TResult>>
        where TCommand : ICommand<TResult>
        where TResult : notnull
    {
    }
}