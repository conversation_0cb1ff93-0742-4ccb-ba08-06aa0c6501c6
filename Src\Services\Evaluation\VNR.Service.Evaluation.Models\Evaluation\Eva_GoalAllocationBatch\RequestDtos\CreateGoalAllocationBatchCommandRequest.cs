using System.ComponentModel.DataAnnotations;

namespace VNR.Service.Evaluation.Models.Evaluation.RequestDtos;

public class CreateGoalAllocationBatchCommandRequest
{
    [Required]
    public Guid GoalId { get; set; }

    [Required]
    public string AllocationMethod { get; set; }

    [Required]
    public string AllocationTypeCode { get; set; }

    public string AllocationFormula { get; set; }

    public string Notes { get; set; }

    public int VersionNumber { get; set; }

    public string AllocatedToPeriodType { get; set; }

    public string AllocatedGoalIds { get; set; }
}
