﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;

namespace VNR.Core.Api.Middleware
{
    public class RequestArgumentsMappingMiddleware
    {
        private readonly RequestDelegate _next;

        public RequestArgumentsMappingMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task Invoke(HttpContext context)
        {
            MappingRequestHeaders(context);
            await _next(context);
        }

        private void MappingRequestHeaders(HttpContext context)
        {
            // Get "lang" route value
            var routeLang = context.GetRouteValue("lang")?.ToString();

            // Get language from the header if not in route
            var languageCode = routeLang ?? context.Request.Headers["languagecode"];

            if (!string.IsNullOrEmpty(languageCode))
            {
                context.Request.Headers["accept-language"] = languageCode;
                CultureInfo.CurrentUICulture = new CultureInfo(languageCode);
            }
        }
    }
}
