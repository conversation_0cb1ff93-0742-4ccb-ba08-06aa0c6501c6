﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AssemblyTitle>VNR.Core.Api</AssemblyTitle>
    <Product>VNR.Core.Api</Product>
    <Copyright>Copyright ©  2022</Copyright>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Base\BaseWebApiConfig.cs" />
    <Compile Remove="Middleware\CorsConfig.cs" />
    <Compile Remove="Middleware\RequestArgumentsMappingDelegatingHandler.cs" />
    <Compile Remove="Middleware\SignalRConfig.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\VNR.Core.Application\VNR.Core.Application.csproj" />
    <ProjectReference Include="..\VNR.Core\VNR.Core.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="MediatR" Version="12.5.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.IO.RecyclableMemoryStream" Version="3.0.1" />
    <PackageReference Include="NSwag.AspNetCore" Version="14.3.0" />
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="System.Runtime.Caching" Version="9.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.SystemWebAdapters" Version="1.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.3.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.1" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\jquery-1.6.4-vsdoc.js" />
    <Content Include="Scripts\jquery-1.6.4.js" />
    <Content Include="Scripts\jquery-1.6.4.min.js" />
    <Content Include="Scripts\jquery.signalR-2.4.3.js" />
    <Content Include="Scripts\jquery.signalR-2.4.3.min.js" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="10.3.4" />
    <PackageReference Include="jQuery" Version="3.7.1" />
    <PackageReference Include="Microsoft.AspNet.SignalR.JS" Version="2.4.3" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
    <PackageReference Include="Microsoft.IdentityModel.Abstractions" Version="8.3.1" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="8.3.1" />
    <PackageReference Include="Microsoft.IdentityModel.Logging" Version="8.3.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="8.3.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.3.1" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.3.1" />
    <PackageReference Include="MultipartDataMediaFormatter.V2.2" Version="2.2.0" />
    <PackageReference Include="Namotion.Reflection" Version="3.3.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NJsonSchema" Version="11.2.0" />
    <PackageReference Include="NSwag.Annotations" Version="14.3.0" />
    <PackageReference Include="NSwag.Core" Version="14.3.0" />
    <PackageReference Include="NSwag.Generation" Version="14.3.0" />
    <PackageReference Include="NSwag.Generation.WebApi" Version="14.3.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.3.1" />
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe" Version="6.0.0" />
    <PackageReference Include="System.Text.Encodings.Web" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Kendo.Mvc">
      <HintPath>..\..\..\FrameworkBinaries\Kendo.Mvc.dll</HintPath>
    </Reference>
  </ItemGroup>
</Project>