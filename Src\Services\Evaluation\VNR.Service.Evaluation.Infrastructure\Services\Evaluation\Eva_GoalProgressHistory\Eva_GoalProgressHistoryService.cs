using Microsoft.EntityFrameworkCore;
using EvaGoalProgressHistoryEntity = VNR.Core.Domain.Entities.Evaluation.Eva_GoalProgressHistory;
using VNR.Core.Domain.Repository;
using VNR.Core.Common.Logging;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalProgressHistory;

public class Eva_GoalProgressHistoryService : IEva_GoalProgressHistoryService
{
    private readonly IGenericRepository<EvaGoalProgressHistoryEntity, Guid> _repository;
    private readonly ILoggingService _loggingService;

    public Eva_GoalProgressHistoryService(IGenericRepository<EvaGoalProgressHistoryEntity, Guid> repository, ILoggingService loggingService)
    {
        _repository = repository;
        _loggingService = loggingService;
    }

    public async Task<IEnumerable<EvaGoalProgressHistoryEntity>> GetAllAsync()
    {
        await _loggingService.LogInformationAsync("Getting all Eva_GoalProgressHistory entities");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<EvaGoalProgressHistoryEntity?> GetByIdAsync(Guid id)
    {
        await _loggingService.LogInformationAsync($"Getting Eva_GoalProgressHistory by id {id}");
        return await _repository.GetByIdAsync(id);
    }

    public async Task<Guid> CreateAsync(EvaGoalProgressHistoryEntity entity)
    {
        await _repository.AddAsync(entity);
        await _loggingService.LogInformationAsync($"Created Eva_GoalProgressHistory with id {entity.Id}");
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(EvaGoalProgressHistoryEntity entity)
    {
        await _repository.UpdateAsync(entity);
        await _loggingService.LogInformationAsync($"Updated Eva_GoalProgressHistory with id {entity.Id}");
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var result = await _repository.DeleteAsync(id);
        await _loggingService.LogInformationAsync($"Deleted Eva_GoalProgressHistory with id {id}");
        return result;
    }
}
