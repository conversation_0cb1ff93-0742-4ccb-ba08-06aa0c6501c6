﻿using System;
using System.Text.Json;

namespace VNR.Core.Api.Middleware.FirendlyExceptions
{
    public class DefaultTransform : ITransform
    {
        public int StatusCode => 500; // Default status for unhandled exceptions
        public string ContentType => "application/json";

        public string GetContent(Exception ex)
        {
            return JsonSerializer.Serialize(new { error = ex.Message });
        }
    }


}
