﻿using Kendo.Mvc;
using Kendo.Mvc.Infrastructure;
using Kendo.Mvc.UI;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Microsoft.AspNetCore.WebUtilities;
using Newtonsoft.Json;
using VNR.Core.Models.Base;

namespace VNR.Core.Application.Attributes;

public class KendoGridRequestModelBinder : IModelBinder
{
    public Task BindModelAsync(ModelBindingContext bindingContext)
    {
        var request = bindingContext.HttpContext.Request;

        var targetType = bindingContext.ModelType;

        //var queryModel = Activator.CreateInstance(targetType) as KendoGridRequestModel;
        var jsonContent = new StreamReader(bindingContext.HttpContext.Request.Body).ReadToEndAsync();
        var queryModel = JsonConvert.DeserializeObject(jsonContent.Result, targetType) as KendoGridRequestModel;

        if (queryModel == null)
        {
            throw new InvalidOperationException("The model is null or not of type KendoGridRequestModel.");
        }

        // You can call the existing model binder here to populate the properties from [FromBody] if needed.

        var kendoGridQueryParams = queryModel.DataSourceRequestString ?? request.QueryString.Value;

        if (!string.IsNullOrEmpty(kendoGridQueryParams))
        {
            BindModel(queryModel, kendoGridQueryParams);
        }

        bindingContext.Result = ModelBindingResult.Success(queryModel);

        return Task.CompletedTask;
    }

    private void BindModel(KendoGridRequestModel requestModel, string kendoGridQueryParams)
    {
        var queryString = QueryHelpers.ParseQuery(kendoGridQueryParams);

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.Sort, out var result))
        {
            requestModel.Sorts = DataSourceDescriptorSerializer.Deserialize<SortDescriptor>(result);
        }

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.Page, out var result2) && int.TryParse(result2, out var page))
        {
            requestModel.Page = page;
        }

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.PageSize, out var result3) && int.TryParse(result3, out var pageSize))
        {
            requestModel.PageSize = pageSize;
        }

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.Filter, out var result4))
        {
            requestModel.Filters = FilterDescriptorFactory.Create(result4);
        }

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.Group, out var result5))
        {
            requestModel.Groups = DataSourceDescriptorSerializer.Deserialize<GroupDescriptor>(result5);
        }

        if (queryString.TryGetValue(DataSourceRequestUrlParameters.Aggregates, out var result6))
        {
            requestModel.Aggregates = DataSourceDescriptorSerializer.Deserialize<AggregateDescriptor>(result6);
        }
    }
}