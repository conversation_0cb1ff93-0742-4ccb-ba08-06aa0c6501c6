﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Hosting;
using VNR.Core.Models;
using Newtonsoft.Json;
using System.Net;
using Microsoft.Extensions.Hosting;
using VNR.Core.Models.Enums;
using VNR.Core.Common.Logging;

namespace VNR.Core.Api.Middleware
{
    /// <summary>
    /// Modern replacement for WebApi2ExceptionProvider that extracts exceptions from HttpContext
    /// </summary>
    public class VnrExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<VnrExceptionHandler> _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IWebHostEnvironment _environment;
        private readonly ILoggingService _loggingService;

        public VnrExceptionHandler(ILogger<VnrExceptionHandler> logger, IHttpContextAccessor httpContextAccessor, IWebHostEnvironment environment, ILoggingService loggingService)
        {
            _logger = logger;
            _httpContextAccessor = httpContextAccessor;
            _environment = environment;
            _loggingService = loggingService;
        }

        public async ValueTask<bool> TryHandleAsync(HttpContext httpContext, Exception exception, CancellationToken cancellationToken)
        {
            try
            {
                // Store the exception for later retrieval
                httpContext.Items["ExceptionContext"] = exception;

                // Log error using unified logging service
                try
                {
                    await _loggingService.LogErrorAsync("Unhandled exception occurred", exception, httpContext, httpContext.TraceIdentifier);
                }
                catch (Exception logEx)
                {
                    // Fallback to ILogger if unified logging service fails
                    _logger.LogError(exception, "Unhandled exception occurred: {Message}", exception.Message);
                    _logger.LogError(logEx, "Error logging exception with unified logging service");
                }

                // Set HTTP status code
                httpContext.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                httpContext.Response.ContentType = "application/json";

                var jsonSettings = new JsonSerializerSettings
                {
                    Formatting = Formatting.Indented
                };

                // Prepare response object based on environment
                bool isRequestFromLocal = _environment.IsDevelopment() || _environment.IsEnvironment("server.IsLocal");

#if DEBUG
                isRequestFromLocal = true;
#endif

                var resultObject = new ApiResult
                {
                    Status = ApiResultStatus.EXCEPTION,
                    Message = "An error occurred"
                };

                if (isRequestFromLocal)
                {
                    var rootException = exception.GetBaseException();
                    resultObject.Data = new
                    {
                        ErrorRoot = rootException.ToString(),
                        ErrorDetails = exception.ToString()
                    };
                }
                else
                {
                    resultObject.Data = new
                    {
                        ErrorRoot = "Internal Server Error",
                        ErrorDetails = "An error occurred"
                    };
                }

                // Serialize and write the response
                var jsonResponse = JsonConvert.SerializeObject(resultObject, jsonSettings);
                await httpContext.Response.WriteAsync(jsonResponse, cancellationToken);

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in exception handler");
                return false;
            }
        }

        /// <summary>
        /// Gets an exception stored in the HttpContext.Items collection
        /// </summary>
        public Exception GetException()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items != null && httpContext.Items.TryGetValue("ExceptionContext", out var value))
            {
                return value as Exception;
            }
            return null;
        }
    }
}
