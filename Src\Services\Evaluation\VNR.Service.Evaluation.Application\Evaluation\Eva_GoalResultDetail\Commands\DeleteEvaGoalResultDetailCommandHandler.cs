using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Commands;

public class DeleteEvaGoalResultDetailCommandHandler : CommandHandler<DeleteEvaGoalResultDetailCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultDetailService _service;

    public DeleteEvaGoalResultDetailCommandHandler(IEva_GoalResultDetailService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaGoalResultDetailCommand request,
        CancellationToken cancellationToken)
    {
        var result = await _service.DeleteAsync(request.ID);

        if (result)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully deleted Eva_GoalResultDetail: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResultDetail not found for deletion: {request.ID}");
        }

        return Success(result, result ? "Delete Eva_GoalResultDetail Successful" : "Eva_GoalResultDetail not found");
    }
}