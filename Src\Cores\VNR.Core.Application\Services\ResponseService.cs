using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Core.Models.Enums;

namespace VNR.Core.Application.Services
{
    public static class ResponseService
    {
        public static IApiResult ResultObject(object data, string message = null, ApiResultStatus status = ApiResultStatus.SUCCESS)
        {
            return new ApiResult()
            {
                Data = data,
                Message = message,
                Status = status
            };
        }

        public static IApiResult<T> Result<T>(T data, string message = null, ApiResultStatus status = ApiResultStatus.SUCCESS)
        {
            return new ApiResult<T>()
            {
                Data = data,
                Message = message,
                Status = status
            };
        }

        public static IApiResult<T> Error<T>(string message, T data = default)
        {
            return Result(data, message, ApiResultStatus.FAIL);
        }

        public static IApiResult<T> Exception<T>(string message, T data = default)
        {
            return Result(data, message, ApiResultStatus.EXCEPTION);
        }
        
        public static IApiResult<T> Success<T>(T data, string message = "")
        {
            return Result(data, message);
        }

        public static IApiResult<BaseResponseGridModel<T>> ResultKendoGrid<T>(KendoGridRequestModel requestModel,
            IEnumerable<T> data, string message = null,
            ApiResultStatus status = ApiResultStatus.SUCCESS) where T : BaseDto
        {
            var result = new BaseResponseGridModel<T>(requestModel, data);
            return Result(result, message, status);
        }
    }
} 