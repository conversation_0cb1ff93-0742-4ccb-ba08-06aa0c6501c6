using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinGoal.RequestDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Commands;

public class DeleteEvaCheckinGoalCommandHandler : CommandHandler<DeleteEvaCheckinGoalCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinGoalService _service;

    public DeleteEvaCheckinGoalCommandHandler(IEva_CheckinGoalService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaCheckinGoalCommand request,
        CancellationToken cancellationToken)
    {
        var res = await _service.DeleteAsync(request.ID);
        return Result(res);
    }
}