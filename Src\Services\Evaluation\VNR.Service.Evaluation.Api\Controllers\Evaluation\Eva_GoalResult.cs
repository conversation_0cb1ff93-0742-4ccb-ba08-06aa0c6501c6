﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Result Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalResult]", Description = "APIs kết quả mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalResultController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalResultController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Goal Results
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Goal Results</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalResultQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Goal Result
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Result</param>
    /// <returns>Id của Eva Goal Result mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalResultCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Goal Result theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalResultByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Result
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalResultCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Result
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalResultCommand command)
        => await HandleRequest(command);
}