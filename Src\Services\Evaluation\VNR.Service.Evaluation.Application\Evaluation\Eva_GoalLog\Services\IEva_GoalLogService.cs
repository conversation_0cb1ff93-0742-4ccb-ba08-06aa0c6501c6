using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Services;

public interface IEva_GoalLogService
{
    Task<IEnumerable<Core.Domain.Entities.Evaluation.Eva_GoalLog>> GetAllAsync();
    Task<Core.Domain.Entities.Evaluation.Eva_GoalLog?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(Core.Domain.Entities.Evaluation.Eva_GoalLog entity);
    Task<bool> UpdateAsync(Core.Domain.Entities.Evaluation.Eva_GoalLog entity);
    Task<bool> DeleteAsync(Guid id);
}
