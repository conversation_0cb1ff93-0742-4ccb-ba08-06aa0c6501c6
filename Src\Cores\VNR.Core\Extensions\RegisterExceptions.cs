﻿using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterExceptions(this IServiceCollection services)
        {
            services.AddExceptionHandler(options =>
             {
                 options.ExceptionHandlingPath = "/error";
                 options.AllowStatusCode404Response = true;
             });

            return services;
        }
    }
}