{"AuditLog": {"EnableDatabase": true, "ConnectionStringName": "AuditConnection", "TableName": "AuditLogs", "EnableMessageQueue": false, "MessageQueueSettings": {"ConnectionStringName": "QueueConnectionString", "QueueName": "audit-logs", "ExchangeName": "audit-logs-exchange", "ExchangeType": "direct", "RoutingKey": "audit-logs"}, "RetentionPolicy": {"Enabled": false, "RetentionDays": 365, "ArchiveEnabled": false, "ArchivePath": "Logs/Archive/Audit"}, "BatchSettings": {"Enabled": true, "BatchSize": 100, "FlushIntervalSeconds": 30}, "ElasticsearchSettings": {"Enabled": true, "ConnectionStringName": "ElasticsearchConnectionString", "IndexPrefix": "vnr-audit-logs", "NumberOfShards": 3, "NumberOfReplicas": 1, "RefreshInterval": "10s", "BatchPostSize": 50, "Period": "00:00:10"}, "FileSettings": {"Enabled": true, "Path": "Logs/audit-.log", "RollingInterval": "Day", "RetainedFileCountLimit": 365, "FileSizeLimitBytes": ********, "RollOnFileSizeLimit": true, "Shared": true, "FlushToDiskInterval": "00:00:05"}, "AuditNetSettings": {"Enabled": true, "EventType": "AuditEvent", "IncludeEnvironmentVariables": true, "IncludeMachineName": true, "IncludeUserName": true, "IncludeClientIp": true, "IncludeUserAgent": true, "IncludeRequestBody": true, "IncludeResponseBody": false, "IncludeHeaders": true, "IncludeQueryString": true}, "FilterSettings": {"ExcludePaths": ["/health", "/health/ready", "/health/live", "/metrics", "/favicon.ico", "/swagger", "/swagger-ui", "/api-docs"], "ExcludeMethods": ["OPTIONS", "HEAD"], "ExcludeStatusCodes": [404], "IncludeOnlyPaths": [], "IncludeOnlyMethods": [], "IncludeOnlyStatusCodes": []}, "EnrichmentSettings": {"IncludeStackTrace": false, "IncludeInnerExceptions": true, "IncludeMachineName": true, "IncludeEnvironment": true, "IncludeApplication": true, "IncludeVersion": true, "IncludeCorrelationId": true, "IncludeUserId": true, "IncludeUserName": true, "IncludeClientIp": true, "IncludeUserAgent": true, "IncludeReferer": true, "IncludeQueryString": true, "IncludeRequestBody": false, "IncludeResponseBody": false, "IncludeHeaders": false, "IncludeCookies": false, "IncludeSession": false}, "RetentionSettings": {"EnableRetention": true, "RetentionDays": 90, "ArchiveBeforeDelete": true, "ArchivePath": "Logs/Archive/Audit"}, "PerformanceSettings": {"EnableBatching": true, "BatchSize": 100, "BatchTimeoutSeconds": 30, "EnableAsyncProcessing": true, "MaxConcurrency": 10}, "SecuritySettings": {"MaskSensitiveData": true, "SensitiveFields": ["password", "token", "secret", "key", "creditcard", "ssn"], "HashSensitiveData": false, "EncryptSensitiveData": false}}}