{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"VnrConnectionString": "Data Source=***********,1968;Initial Catalog=HRM10_UPGRADE;User ID=sa;Password=********************************************;TrustServerCertificate=True;"}, "AppSettings": {"ModeProfilerLog": "ALL", "AllowOrigin": "https://localhost:7000;https://localhost:7000;https://localhost:7001;https://localhost:7002;https://localhost:7003;https://localhost:7005"}, "BasicAuthenticationConfiguration": {"Enabled": false}, "OwinJwtBearerServerConfiguration": {"Enabled": true, "Audiences": [{"ClientId": "01f52b24e32546ebb87dd5adac97ba37"}]}, "OwinJwtBearerConfiguration": [{"Enabled": true, "Issuer": "https://localhost:7000", "Audience": "01f52b24e32546ebb87dd5adac97ba37", "Secret": "mHnOb9nbnhZfFp8ZeJO3HK5xOX6RvMKEdIGuaXoicD8"}, {"Enabled": true, "IssuerType": "OPENID_CONNECT", "Issuer": "https://qc-core.vnrlocal.com:3005"}], "NswagIdentityServerConfiguration": {"Clients": [{"Enabled": true, "Id": "vnr.service.sample.api", "Description": "HRM Service Sample WebApi", "Authority": "https://qc-core.vnrlocal.com:3005", "ClientId": "vnr_service_sample_api"}]}}