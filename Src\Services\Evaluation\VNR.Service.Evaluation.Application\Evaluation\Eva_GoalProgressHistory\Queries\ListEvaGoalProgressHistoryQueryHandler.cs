using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalProgressHistory.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Queries;

public class ListEvaGoalProgressHistoryQueryHandler : QueryListHandler<ListEvaGoalProgressHistoryQuery, ListEvaGoalProgressHistoryDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalProgressHistoryService _service;

    public ListEvaGoalProgressHistoryQueryHandler(IEva_GoalProgressHistoryService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaGoalProgressHistoryDto>>> Handle(ListEvaGoalProgressHistoryQuery request,
        CancellationToken cancellationToken)
    {
        var listData = await _service.GetAllAsync();
        var result = _context.Mapper.Map<IEnumerable<ListEvaGoalProgressHistoryDto>>(listData);
        
        await _context.LoggingService.LogInformationAsync($"Retrieved {listData.Count()} Eva_GoalProgressHistory records");
        
        return ResultKendoGrid(request.Request, result);
    }
}
