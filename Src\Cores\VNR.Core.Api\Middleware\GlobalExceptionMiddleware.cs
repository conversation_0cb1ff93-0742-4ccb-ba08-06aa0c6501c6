﻿using System;
using System.Net;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using VNR.Core.Common.Logging;
using VNR.Core.Models;
using VNR.Core.Models.Enums;

namespace VNR.Core.Api.Middleware
{
    public class GlobalExceptionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IWebHostEnvironment _environment;
        private readonly ILoggingService _loggingService;
        private readonly Func<HttpContext, Exception, bool> _exceptionHandler;

        public GlobalExceptionMiddleware(
            RequestDelegate next,
            IWebHostEnvironment environment,
            ILoggingService loggingService)
        {
            _next = next;
            _environment = environment;
            _loggingService = loggingService;
            _exceptionHandler = ExceptionHandlerConfig.GetExceptionHandler(environment);
        }

        public async Task Invoke(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                // Log error with context using unified logging service
                try
                {
                    await _loggingService.LogErrorAsync("An unhandled exception occurred", ex, context, context.TraceIdentifier);
                }
                catch (Exception logEx)
                {
                    // Fallback logging if logging service fails
                    Console.WriteLine($"Error logging exception: {logEx.Message}");
                }

                if (!context.Response.HasStarted)
                {
                    if (!_exceptionHandler(context, ex))
                    {
                        await DefaultHandleExceptionAsync(context, ex);
                    }
                }
                else
                {
                    // Fallback logging if response has already started
                    Console.WriteLine("Response has already started, unable to handle exception properly.");
                    throw; // Re-throw if response has already started
                }
            }
        }

        /// <summary>
        /// Handle Exception
        /// </summary>
        /// <param name="context"></param>
        /// <param name="ex"></param>
        /// <returns></returns>
        private async Task DefaultHandleExceptionAsync(HttpContext context, Exception ex)
        {
            context.Response.ContentType = "application/json";
            context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

            var jsonSettings = new JsonSerializerSettings()
            {
                Formatting = Formatting.Indented
            };

            object resultObject;
            bool isRequestFromLocal = _environment.IsDevelopment() || _environment.IsEnvironment("server.IsLocal");

#if DEBUG
            isRequestFromLocal = true;
#endif

            if (isRequestFromLocal)
            {
                resultObject = new ApiResult()
                {
                    Status = ApiResultStatus.EXCEPTION,
                    Data = new
                    {
                        ErrorRoot = ex.GetBaseException().ToString(),
                        ErrorDetails = ex.ToString()
                    },
                    Message = "An unexpected error occurred."
                };
            }
            else
            {
                resultObject = new ApiResult()
                {
                    Status = ApiResultStatus.EXCEPTION,
                    Data = new
                    {
                        ErrorRoot = "Internal Server Error",
                        ErrorDetails = "An error occurred"
                    },
                    Message = "An unexpected error occurred."
                };
            }

            string jsonResponse = JsonConvert.SerializeObject(resultObject, jsonSettings);
            await context.Response.WriteAsync(jsonResponse);
        }
    }
}