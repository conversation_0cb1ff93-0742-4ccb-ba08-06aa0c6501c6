using MediatR;
using Microsoft.Extensions.DependencyInjection;
using VNR.Core.Application.MediatR;

namespace VNR.Core.Application.Extensions
{
    public static class CrudHandlerBehaviorExtensions
    {
        /// <summary>
        /// Đăng ký CrudHandlerBehavior để tự động sử dụng CrudHandler khi không tìm thấy handler cụ thể
        /// </summary>
        public static IServiceCollection RegisterCrudHandlerBehavior(this IServiceCollection services)
        {
            // Đăng ký CrudHandlerBehavior như một pipeline behavior
            services.AddTransient(typeof(IPipelineBehavior<,>), typeof(CrudHandlerBehavior<,>));
            
            return services;
        }
    }
} 