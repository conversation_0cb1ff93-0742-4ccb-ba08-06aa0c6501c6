﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using VNR.Core.Common.Services;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterProviders(this IServiceCollection services)
        {
            Console.WriteLine("Register Providers");
            services.AddScoped<IDatabaseProviderService, DatabaseProviderService>();

            return services;
        }
    }
}