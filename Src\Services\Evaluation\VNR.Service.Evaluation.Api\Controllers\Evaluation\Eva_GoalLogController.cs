﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalLog.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Log Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalLog]", Description = "APIs nhật ký mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalLogController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalLogController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Goal Logs
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Goal Logs</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalLogQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Goal Log
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Log</param>
    /// <returns>Id của Eva Goal Log mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalLogCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Goal Log theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalLogByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Log
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalLogCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Log
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalLogCommand command)
        => await HandleRequest(command);
}