﻿using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSwag.AspNetCore;
using VNR.Core.Configurations.Services;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static void UseCustomSwagger(this IApplicationBuilder app, IConfiguration configuration, List<string> assemblyNames)
        {
            var configService = app.ApplicationServices.GetRequiredService<IConfigurationService>();
            var idsConfig = configService?.Authentication?.NswagIdentityServerConfiguration;
            var idsClient = idsConfig?.Clients?.Where(p => p.Enabled)?.FirstOrDefault();

            var assemblyName = assemblyNames.FirstOrDefault();

            // Cấu hình Swagger
            app.UseOpenApi();

            app.UseSwaggerUi(settings =>
            {
                settings.DocumentTitle = $"{assemblyName} API";
                settings.OAuth2Client = new OAuth2ClientSettings
                {
                    AppName = $"{assemblyName} API",
                    ClientId = idsClient.ClientId,
                    ClientSecret = idsClient.ClientSecret,
                    UsePkceWithAuthorizationCodeGrant = true
                };
#if DEBUG
                settings.OAuth2Client.ClientSecret = "secret";
#endif

                //// Add all API versions to the Swagger UI
                //settings.SwaggerRoutes.Add(new SwaggerUiRoute("v1", "/swagger/v1/swagger.json"));
                //settings.SwaggerRoutes.Add(new SwaggerUiRoute("v2", "/swagger/v2/swagger.json"));

                //// Set the path for Swagger UI
                //settings.Path = "/swagger";
            });
        }
    }
}