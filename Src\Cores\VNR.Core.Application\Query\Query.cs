﻿using Microsoft.AspNetCore.Mvc;
using VNR.Core.Models.Base;

namespace VNR.Core.Application.Query
{
    public class Query<TResult> : IQuery<TResult>
        where TResult : BaseDto
    {
    }

    public class Query<TId, TResult> : IQuery<TResult>
        where TResult : BaseDto
        where TId : IEquatable<TId>
    {
        [FromRoute]
        public TId ID { get; set; }

        public Query()
        {
        }

        public Query(TId ID)
        {
            this.ID = ID;
        }
    }
}