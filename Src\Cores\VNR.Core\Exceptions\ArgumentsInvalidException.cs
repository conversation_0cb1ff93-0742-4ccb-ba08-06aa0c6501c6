﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Collections.Generic;
using VNR.Core.Models.Enums;

namespace VNR.Core.Exceptions
{
    public class ArgumentsValidatorException : Exception
    {
        [JsonObject(NamingStrategyType = typeof(CamelCaseNamingStrategy))]
        public class ParamDetails
        {
            public ParamDetails()
            {
            }

            public ParamDetails(string name, string description)
            {
                Name = name;
                Description = description;
            }

            public string Name { get; set; }
            public string Description { get; set; }
        }


        public List<ParamDetails> Parameters { get; set; } = new List<ParamDetails>();
        public ErrorCode Code { get; set; }
        public string Description { get; set; }

        public ArgumentsValidatorException AddParamDetails(string name, string description)
        {
            Parameters.Add(new ParamDetails(name, description));
            return this;
        }

        public ArgumentsValidatorException SetErrorCode(ErrorCode code, string description)
        {
            Code = code;
            Description = description;
            return this;
        }
    }
}
