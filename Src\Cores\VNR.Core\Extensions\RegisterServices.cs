using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        /// <summary>
        /// Tự động tìm và đăng ký các assembly chứa services
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="lifetime">Lifetime của service</param>
        /// <returns>Service collection</returns>
        public static IServiceCollection RegisterServices(this IServiceCollection services, ServiceLifetime lifetime = ServiceLifetime.Scoped)
        {
            // Lấy tất cả các assembly đã được load
            var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                .Where(a => a.FullName.StartsWith("VNR.") || a.FullName.StartsWith("HRM."))
                .ToList();

            // Tìm các assembly chứa interface service
            var serviceAssemblies = assemblies
                .Where(a => a.GetTypes()
                    .Any(t => t.IsInterface && t.Name.StartsWith("I") && t.Name.EndsWith("Service")))
                .ToList();

            return services.RegisterServiceByAssembly(lifetime, serviceAssemblies.ToArray());
        }

        /// <summary>
        /// Đăng ký tự động các service theo quy ước đặt tên
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="assemblies">Danh sách assembly cần quét</param>
        /// <returns>Service collection</returns>
        public static IServiceCollection RegisterServiceByAssembly(this IServiceCollection services, params Assembly[] assemblies)
        {
            if (assemblies == null || assemblies.Length == 0)
            {
                assemblies = new[] { Assembly.GetCallingAssembly() };
            }

            foreach (var assembly in assemblies)
            {
                // Tìm tất cả các interface có prefix là "I" và có suffix là "Service"
                var serviceTypes = assembly.GetTypes()
                    .Where(t => t.IsInterface && t.Name.StartsWith("I") && t.Name.EndsWith("Service"))
                    .ToList();

                foreach (var serviceType in serviceTypes)
                {
                    // Tìm implementation tương ứng (bỏ prefix "I")
                    var implementationType = assembly.GetTypes()
                        .FirstOrDefault(t => !t.IsInterface && !t.IsAbstract && t.Name == serviceType.Name.Substring(1));

                    if (implementationType != null)
                    {
                        // Đăng ký service với lifetime là Scoped
                        services.AddScoped(serviceType, implementationType);
                    }
                }
            }

            return services;
        }

        /// <summary>
        /// Đăng ký tự động các service theo quy ước đặt tên với lifetime tùy chỉnh
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="lifetime">Lifetime của service</param>
        /// <param name="assemblies">Danh sách assembly cần quét</param>
        /// <returns>Service collection</returns>
        public static IServiceCollection RegisterServiceByAssembly(this IServiceCollection services, ServiceLifetime lifetime, params Assembly[] assemblies)
        {
            if (assemblies == null || assemblies.Length == 0)
            {
                assemblies = new[] { Assembly.GetCallingAssembly() };
            }

            foreach (var assembly in assemblies)
            {
                var serviceTypes = assembly.GetTypes()
                    .Where(t => t.IsInterface && t.Name.StartsWith("I") && t.Name.EndsWith("Service"))
                    .ToList();

                foreach (var serviceType in serviceTypes)
                {
                    var implementationType = assembly.GetTypes()
                        .FirstOrDefault(t => !t.IsInterface && !t.IsAbstract && t.Name == serviceType.Name.Substring(1));

                    if (implementationType != null)
                    {
                        switch (lifetime)
                        {
                            case ServiceLifetime.Singleton:
                                services.AddSingleton(serviceType, implementationType);
                                break;
                            case ServiceLifetime.Scoped:
                                services.AddScoped(serviceType, implementationType);
                                break;
                            case ServiceLifetime.Transient:
                                services.AddTransient(serviceType, implementationType);
                                break;
                        }
                    }
                    else
                    {
                        throw new Exception(
                            $"Không tìm thấy Services sử dụng Interface hoặc tên của Class & Interface được đặt không đúng quy tắc.");
                    }
                }
            }

            return services;
        }
    }
} 