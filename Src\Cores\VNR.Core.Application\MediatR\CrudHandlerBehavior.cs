using MediatR;
using Microsoft.Extensions.DependencyInjection;
using VNR.Core.Application.Command;
using VNR.Core.Application.Query;
using VNR.Core.Business.Base;
using VNR.Core.Domain.Repository;

namespace VNR.Core.Application.MediatR
{
    /// <summary>
    /// MediatR Behavior để tự động fallback về CrudHandler khi không tìm thấy handler
    /// </summary>
    public class CrudHandlerBehavior<TRequest, TResponse> : IPipelineBehavior<TRequest, TResponse>
        where TRequest : IRequest<TResponse>
    {
        private readonly IServiceProvider _serviceProvider;

        public CrudHandlerBehavior(IServiceProvider serviceProvider)
        {
            _serviceProvider = serviceProvider;
        }

        public async Task<TResponse> Handle(TRequest request, RequestHandlerDelegate<TResponse> next, CancellationToken cancellationToken)
        {
            try
            {
                // Thử xử lý bình thường trước
                return await next();
            }
            catch (InvalidOperationException ex) when (ex.Message.Contains("No service for type") && ex.Message.Contains("IRequestHandler"))
            {
                // Nếu không tìm thấy handler, thử sử dụng CrudHandler
                return await HandleWithCrudHandler(request, cancellationToken);
            }
        }

        private async Task<TResponse> HandleWithCrudHandler(TRequest request, CancellationToken cancellationToken)
        {
            var requestType = request.GetType();

            if (requestType.IsGenericType)
            {
                var genericTypeDefinition = requestType.GetGenericTypeDefinition();
                var genericArguments = requestType.GetGenericArguments();

                if (genericTypeDefinition == typeof(CreateCommand<,>))
                {
                    var tRequest = genericArguments[0];
                    var tResult = genericArguments[1];

                    var entityType = GetEntityTypeFromDto(tResult);
                    if (entityType != null)
                    {
                        var keyType = GetKeyType(entityType);
                        var crudHandlerType = typeof(CrudHandler<,,,>).MakeGenericType(tResult, tRequest, entityType, keyType);
                        var crudHandler = CreateCrudHandler(crudHandlerType);

                        var handleMethod = crudHandlerType.GetMethod("Handle", new[] { requestType, typeof(CancellationToken) });
                        var result = handleMethod?.Invoke(crudHandler, new object[] { request, cancellationToken });

                        if (result is Task<TResponse> task)
                            return await task;
                    }
                }
                else if (genericTypeDefinition == typeof(UpdateCommand<,,>))
                {
                    var tKey = genericArguments[0];
                    var tRequest = genericArguments[1];
                    var tResult = genericArguments[2];

                    var entityType = GetEntityTypeFromDto(tResult);
                    if (entityType != null)
                    {
                        var crudHandlerType = typeof(CrudHandler<,,,>).MakeGenericType(tResult, tRequest, entityType, tKey);
                        var crudHandler = CreateCrudHandler(crudHandlerType);

                        var handleMethod = crudHandlerType.GetMethod("Handle", new[] { requestType, typeof(CancellationToken) });
                        var result = handleMethod?.Invoke(crudHandler, new object[] { request, cancellationToken });

                        if (result is Task<TResponse> task)
                            return await task;
                    }
                }
                else if (genericTypeDefinition == typeof(DeleteCommand<,>))
                {
                    var tKey = genericArguments[0];
                    var tResult = genericArguments[1];

                    var entityType = GetEntityTypeFromDto(tResult);
                    if (entityType != null)
                    {
                        var crudHandlerType = typeof(CrudHandler<,,,>).MakeGenericType(tResult, typeof(object), entityType, tKey);
                        var crudHandler = CreateCrudHandler(crudHandlerType);

                        var handleMethod = crudHandlerType.GetMethod("Handle", new[] { requestType, typeof(CancellationToken) });
                        var result = handleMethod?.Invoke(crudHandler, new object[] { request, cancellationToken });

                        if (result is Task<TResponse> task)
                            return await task;
                    }
                }
                else if (genericTypeDefinition == typeof(Query<,>))
                {
                    var tKey = genericArguments[0];
                    var tResult = genericArguments[1];

                    var entityType = GetEntityTypeFromDto(tResult);
                    if (entityType != null)
                    {
                        var crudHandlerType = typeof(CrudHandler<,,,>).MakeGenericType(tResult, typeof(object), entityType, tKey);
                        var crudHandler = CreateCrudHandler(crudHandlerType);

                        var handleMethod = crudHandlerType.GetMethod("Handle", new[] { requestType, typeof(CancellationToken) });
                        var result = handleMethod?.Invoke(crudHandler, new object[] { request, cancellationToken });

                        if (result is Task<TResponse> task)
                            return await task;
                    }
                }
                else if (genericTypeDefinition == typeof(QueryList<,>))
                {
                    var tRequest = genericArguments[0];
                    var tResult = genericArguments[1];

                    var entityType = GetEntityTypeFromDto(tResult);
                    if (entityType != null)
                    {
                        var keyType = GetKeyType(entityType);
                        var crudHandlerType = typeof(CrudHandler<,,,>).MakeGenericType(tResult, tRequest, entityType, keyType);
                        var crudHandler = CreateCrudHandler(crudHandlerType);

                        var handleMethod = crudHandlerType.GetMethod("Handle", new[] { requestType, typeof(CancellationToken) });
                        var result = handleMethod?.Invoke(crudHandler, new object[] { request, cancellationToken });

                        if (result is Task<TResponse> task)
                            return await task;
                    }
                }
            }

            throw new InvalidOperationException($"No handler found for {requestType.Name} and no suitable CrudHandler could be created");
        }

        private Type? GetEntityTypeFromDto(Type dtoType)
        {
            var dtoName = dtoType.Name;
            if (dtoName.EndsWith("Dto"))
            {
                var entityName = dtoName.Replace("Dto", "");

                var assemblies = AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    var entityType = assembly.GetTypes()
                        .FirstOrDefault(t => t.Name == entityName && !t.IsAbstract && !t.IsInterface);
                    if (entityType != null)
                        return entityType;
                }
            }

            return null;
        }

        private Type GetKeyType(Type entityType)
        {
            return typeof(Guid);
        }

        private object CreateCrudHandler(Type crudHandlerType)
        {
            var genericArgs = crudHandlerType.GetGenericArguments();
            var entityType = genericArgs[2];
            var keyType = genericArgs[3];

            var repositoryType = typeof(IGenericRepository<,>).MakeGenericType(entityType, keyType);
            var repository = _serviceProvider.GetRequiredService(repositoryType);
            var unitOfWork = _serviceProvider.GetRequiredService<IUnitOfWork>();
            var mapper = _serviceProvider.GetRequiredService<AutoMapper.IMapper>();

            return Activator.CreateInstance(crudHandlerType, repository, unitOfWork, mapper)!;
        }
    }
}