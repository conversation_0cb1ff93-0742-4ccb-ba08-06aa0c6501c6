using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Allocation Batch Controller
/// </summary>
[OpenApiTag("[Evaluation][GoalAllocationBatch]", Description = "APIs đợt phân bổ mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalAllocationBatcheController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalAllocationBatcheController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Goal Allocation Batches
    /// </summary>
    /// <param name="query">Thông tin lọc</param>
    /// <returns><PERSON>h sách Eva Goal Allocation Batches</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListGoalAllocationBatchQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Tạo mới Eva Goal Allocation Batch
    /// </summary>
    /// <param name="cmd">Thông tin Eva Goal Allocation Batch</param>
    /// <returns>Id của Eva Goal Allocation Batch mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateGoalAllocationBatchCommand cmd)
        => await HandleRequest(cmd);

    /// <summary>
    /// Lấy thông tin Eva Goal Allocation Batch theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetGoalAllocationBatchByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Allocation Batch
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateGoalAllocationBatchCommand cmd)
        => await HandleRequest(cmd);

    /// <summary>
    /// Xóa Eva Goal Allocation Batch
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteGoalAllocationBatchCommand cmd)
        => await HandleRequest(cmd);
}