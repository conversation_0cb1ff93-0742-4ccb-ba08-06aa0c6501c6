{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"VnrConnectionString": "Data Source=***********,1968;Initial Catalog=HRM10_UPGRADE;User ID=sa;Password=********************************************;TrustServerCertificate=True;"}, "AppSettings": {"ModeProfilerLog": "ALL", "AllowOrigin": "https://localhost:7000;https://localhost:7001;https://localhost:7002;https://localhost:7003;https://localhost:7005"}, "RedisConfiguration": {"UseRedisServer": false, "TimeExpireToken": 60, "RedisConnection": "127.0.0.1:6380,syncTimeout=60000,responseTimeout=60000,connectTimeout=10000,allowAdmin=true,password=7Ri7zcOPdPG4thsgyncp7Q==", "RedisPrefixKeyName": "App-VnPay-Net8-Gateway", "UseRedisSyncData": true}, "OwinJwtBearerServerConfiguration": {"Enabled": true, "Audiences": [{"ClientId": "gateway_client_id", "ClientSecret": "gateway_client_secret"}]}, "OwinJwtBearerConfiguration": [{"Enabled": true, "Issuer": "https://localhost:7000", "Audience": "gateway_client_id", "Secret": "gateway_secret_key"}]}