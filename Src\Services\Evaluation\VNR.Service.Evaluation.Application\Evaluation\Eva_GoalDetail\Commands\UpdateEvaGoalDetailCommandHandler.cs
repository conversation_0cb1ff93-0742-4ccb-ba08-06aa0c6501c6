using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalDetail.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalDetail.Commands;

public class UpdateEvaGoalDetailCommandHandler : CommandHandler<UpdateEvaGoalDetailCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalDetailService _service;

    public UpdateEvaGoalDetailCommandHandler(IEva_GoalDetailService service, IApplicationContext context) : base(
        context.Accessor)
    {
        (_service, _context) = (service, context);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaGoalDetailCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null) return Result(false);
        _context.Mapper.Map(request.Request, entity);
        var res = await _service.UpdateAsync(entity);
        return Result(res);
    }
}