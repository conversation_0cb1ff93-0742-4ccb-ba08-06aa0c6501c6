﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using VNR.Core.Common.Enums;
using VNR.Core.Common.Services;
using VNR.Core.Security.Cryptography;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterDbContext<TContext>(this IServiceCollection services, IConfiguration configuration, string connectionName)
            where TContext : DbContext
        {
            string connectionString = configuration.GetConnectionString(connectionName);
            var decryptor = services.BuildServiceProvider()
              .GetRequiredService<IVnrHrmDecryption>();
            var dbProviderService = services.BuildServiceProvider()
              .GetRequiredService<IDatabaseProviderService>();

            services.AddDbContext<TContext>(options =>
            {
                ConfigureDbContextOptions(options, decryptor.DecryptConnectionString(connectionString), dbProviderService);
            });

            return services;
        }

        public static IServiceCollection RegisterDbContext<TContext>(this IServiceCollection services, IConfiguration configuration, string connectionName, IModel initialModel)
            where TContext : DbContext
        {
            string connectionString = configuration.GetConnectionString(connectionName);
            var decryptor = services.BuildServiceProvider()
              .GetRequiredService<IVnrHrmDecryption>();
            var dbProviderService = services.BuildServiceProvider()
              .GetRequiredService<IDatabaseProviderService>();

            services.AddDbContext<TContext>(options =>
            {
                ConfigureDbContextOptions(options, decryptor.DecryptConnectionString(connectionString), dbProviderService);

                // Disable lazy loading
                options.UseLazyLoadingProxies(false);

                // Reduce the size of the model snapshot
                options.ConfigureWarnings(w => w.Ignore(Microsoft.EntityFrameworkCore.Diagnostics.CoreEventId.ManyServiceProvidersCreatedWarning));

                // Performance optimization
                options.EnableSensitiveDataLogging(false);
                options.EnableDetailedErrors(false);
            });

            return services;
        }

        /// <summary>
        /// Cấu hình DbContext options dựa trên database provider
        /// </summary>
        private static void ConfigureDbContextOptions(DbContextOptionsBuilder options, string connectionString, IDatabaseProviderService dbProviderService)
        {
            Console.WriteLine("CONFIG DBCONTEXT", dbProviderService.GetCurrentProvider());
            switch (dbProviderService.GetCurrentProvider())
            {
                case DatabaseProvider.SqlServer:
                    options.UseSqlServer(connectionString, sqlOptions =>
                    {
                        sqlOptions.EnableRetryOnFailure(maxRetryCount: 15, maxRetryDelay: TimeSpan.FromSeconds(30), errorNumbersToAdd: null);
                    });
                    break;

                case DatabaseProvider.PostgreSQL:
                    options.UseNpgsql(connectionString, npgsqlOptions =>
                    {
                        npgsqlOptions.EnableRetryOnFailure(maxRetryCount: 15, maxRetryDelay: TimeSpan.FromSeconds(30), errorCodesToAdd: null);
                    });
                    break;

                default:
                    throw new ArgumentException($"Unsupported database provider: {dbProviderService.GetProviderName()}");
            }
        }
    }
}