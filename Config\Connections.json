{
  "DatabaseProvider": "PostgreSql",
  "ConnectionStrings": {
    "HrmConnectionString": "Host=***********;Port=5432;Database=Test;Username=postgres;Password=********************************************;Trust Server Certificate=true;",
//    "HrmConnectionString": "Data Source=***********,1968;Initial Catalog=HRM_CORE;User ID=sa;Password=********************************************;TrustServerCertificate=True;",
    "AuditLogConnectionString": "Data Source=***********,1968;Initial Catalog=HRM_AuditLogging;User ID=sa;Password=********************************************;TrustServerCertificate=True;",
    "QueueConnectionString": "Data Source=***********,1968;Initial Catalog=HRM_Migration;User ID=sa;Password=********************************************;TrustServerCertificate=True;",
    "KafkaConnectionString": "Data Source=***********,1968;Initial Catalog=HRM_Migration;User ID=sa;Password=********************************************;TrustServerCertificate=True;"
  }
}