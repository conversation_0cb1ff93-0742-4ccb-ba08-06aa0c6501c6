﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Checkin Schedule Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_CheckinSchedule]", Description = "APIs lịch trình checkin")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_CheckinScheduleController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_CheckinScheduleController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Checkin Schedules
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Checkin Schedules</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaCheckinScheduleQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Checkin Schedule
    /// </summary>
    /// <param name="command">Thông tin Eva Checkin Schedule</param>
    /// <returns>Id của Eva Checkin Schedule mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaCheckinScheduleCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Checkin Schedule theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaCheckinScheduleByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Checkin Schedule
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaCheckinScheduleCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Checkin Schedule
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaCheckinScheduleCommand command)
        => await HandleRequest(command);
}