using Microsoft.EntityFrameworkCore;
using VNR.Core.Common.Logging;
using EvaGoalAdjustmentRequestEntity = VNR.Core.Domain.Entities.Evaluation.Eva_GoalAdjustmentRequest;
using VNR.Core.Domain.Repository;
using VNR.Infrastructure.BaseRepositories;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Services;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalAdjustmentRequest
{
    public class Eva_GoalAdjustmentRequestService : IEva_GoalAdjustmentRequestService
    {
        private readonly IGenericRepository<EvaGoalAdjustmentRequestEntity, Guid> _repository;
        private readonly ILoggingService _logging;
        private readonly IUnitOfWork _unitOfWork;

        public Eva_GoalAdjustmentRequestService(
            IGenericRepository<EvaGoalAdjustmentRequestEntity, Guid> repository,
            ILoggingService logging,
            IUnitOfWork unitOfWork)
            => (_repository, _logging, _unitOfWork) = (repository, logging, unitOfWork);

        public async Task<IEnumerable<EvaGoalAdjustmentRequestEntity>> GetAllAsync()
        {
            await _logging.LogInformationAsync("Get all Eva_GoalAdjustmentRequest");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }

        public async Task<EvaGoalAdjustmentRequestEntity?> GetByIdAsync(Guid id) => await _repository.GetByIdAsync(id);

        public async Task<Guid> CreateAsync(EvaGoalAdjustmentRequestEntity entity)
        {
            await _repository.AddAsync(entity);
            await _unitOfWork.SaveChangesAsync();
            return entity.Id;
        }

        public async Task<bool> UpdateAsync(EvaGoalAdjustmentRequestEntity entity)
        {
            await _repository.UpdateAsync(entity);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var res = await _repository.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();
            return res;
        }
    }
}