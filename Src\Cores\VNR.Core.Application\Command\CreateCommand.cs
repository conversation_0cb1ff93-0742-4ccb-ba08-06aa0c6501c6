﻿using Microsoft.AspNetCore.Mvc;

namespace VNR.Core.Application.Command
{
    public abstract class BaseCommand<TResult> : ICommand<TResult>
        where TResult : notnull
    {
        public BaseCommand()
        {
        }
    }

    public class CreateCommand<TRequest, TResult> : ICommand<TResult>
        where TRequest : class, new()
        where TResult : notnull
    {
        [FromBody]
        public TRequest Request { get; set; }

        public CreateCommand()
        {
            Request = new TRequest();
        }

        public CreateCommand(TRequest request)
        {
            Request = request ?? new TRequest();
        }
    }

    public class UpdateCommand<TId, TRequest, TResult> : ICommand<TResult>
        where TId : IEquatable<TId>
        where TRequest : class, new()
        where TResult : notnull
    {
        [FromRoute]
        public TId ID { get; set; }

        [FromBody]
        public TRequest Request { get; set; }

        public UpdateCommand()
        {
            Request = new TRequest();
        }

        public UpdateCommand(TId? id, TRequest? request)
        {
            ID = id ?? ID;
            Request = request ?? new TRequest();
        }
    }

    public class DeleteCommand<TId, TResult> : ICommand<TResult>
        where TId : IEquatable<TId>
        where TResult : notnull
    {
        [FromRoute]
        public TId ID { get; set; }

        public DeleteCommand()
        {
        }

        public DeleteCommand(TId? id)
        {
            ID = id ?? ID;
        }
    }
}