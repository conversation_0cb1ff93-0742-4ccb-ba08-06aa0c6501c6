﻿using System;
using System.Collections.Generic;

namespace VNR.Core.Api.Middleware.FirendlyExceptions
{
    public class DefaultTransformsCollection : ITransformsCollection
    {
        private readonly Dictionary<Type, ITransform> _transforms = new();

        public DefaultTransformsCollection()
        {
            // Register default exception transformation
            _transforms[typeof(Exception)] = new DefaultTransform();
        }

        public void AddTransform<TException>(ITransform transform) where TException : Exception
        {
            _transforms[typeof(TException)] = transform;
        }

        public ITransform FindTransform(Exception ex)
        {
            return _transforms.ContainsKey(ex.GetType()) ? _transforms[ex.GetType()] : new DefaultTransform();
        }
    }
}