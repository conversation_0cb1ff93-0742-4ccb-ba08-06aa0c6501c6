using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalResult.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Queries;

public class GetEvaGoalResultByIdQueryHandler : QueryHandler<GetEvaGoalResultByIdQuery, EvaGoalResultDto>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultService _service;

    public GetEvaGoalResultByIdQueryHandler(IEva_GoalResultService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<EvaGoalResultDto>> Handle(GetEvaGoalResultByIdQuery request,
        CancellationToken cancellationToken)
    {
        await _context.LoggingService.LogInformationAsync($"Getting Eva_GoalResult by ID: {request.ID}");

        var entity = await _service.GetByIdAsync(request.ID);

        if (entity != null)
        {
            await _context.LoggingService.LogInformationAsync($"Successfully retrieved Eva_GoalResult: {request.ID}");
        }
        else
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResult not found: {request.ID}");
        }

        var result = _context.Mapper.Map<EvaGoalResultDto>(entity);
        return Result(result);
    }
}
