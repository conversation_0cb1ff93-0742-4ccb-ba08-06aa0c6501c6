using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;

namespace VNR.Service.Evaluation.Application.Category.Commands;

public class UpdateGoalPeriodCommandHandler : CommandHandler<UpdateGoalPeriodCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly ICat_GoalPeriodService _goalPeriodService;

    public UpdateGoalPeriodCommandHandler(ICat_GoalPeriodService goalPeriodService,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _goalPeriodService) = (applicationContext, goalPeriodService);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateGoalPeriodCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _goalPeriodService.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _applicationContext.LoggingService.LogWarningAsync($"GoalPeriod not found: {request.ID}");
            return Result(false);
        }

        _applicationContext.Mapper.Map(request.Request, entity);

        var res = await _goalPeriodService.UpdateAsync(entity);
        return Result(res);
    }
}