using VNR.Core.Application.Command;
using VNR.Core.Domain.Entities.Category;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Category.Services;

namespace VNR.Service.Evaluation.Application.Category.Commands;

public class CreateGoalPeriodCommandHandler : CommandHandler<CreateGoalPeriodCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly ICat_GoalPeriodService _goalPeriodService;

    public CreateGoalPeriodCommandHandler(ICat_GoalPeriodService goalPeriodService,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _goalPeriodService) = (applicationContext, goalPeriodService);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateGoalPeriodCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Cat_GoalPeriod>(request.Request);
        var id = await _goalPeriodService.CreateAsync(entity);
        return Result(id);
    }
}