using VNR.Core.Models.Base;

namespace VNR.Service.Evaluation.Models.Evaluation.Eva_GoalResult.RequestDtos;

public class ListEvaGoalResultQueryRequest : BaseRequestGridModel
{
    public Guid? GoalId { get; set; }
    public Guid? GoalPeriodId { get; set; }
    public Guid? ProfileId { get; set; }
    public string Status { get; set; }
    public DateTimeOffset? FromDate { get; set; }
    public DateTimeOffset? ToDate { get; set; }
}
