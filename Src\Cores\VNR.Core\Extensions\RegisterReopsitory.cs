﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        /// <summary>
        /// Tự động đăng ký tất cả các repository từ các assembly infrastructure
        /// dựa trên mẫu đặt tên và giao diện
        /// </summary>
        /// <param name="services">Collection dịch vụ</param>
        /// <param name="assemblies">Danh sách assembly cần quét</param>
        /// <param name="lifetime">Vòng đời của service</param>
        /// <returns>IServiceCollection</returns>
        public static IServiceCollection RegisterRepositories(
            this IServiceCollection services,
            IEnumerable<Assembly> assemblies,
            ServiceLifetime lifetime = ServiceLifetime.Scoped)
        {
            if (assemblies == null || !assemblies.Any())
            {
                return services;
            }

            // Lọc các assembly infrastructure chứa repository implementations
            var infrastructureAssemblies = assemblies
                .Where(a => a.GetName().Name?.EndsWith(".Infrastructure") == true)
                .ToList();

            foreach (var assembly in infrastructureAssemblies)
            {
                // Tìm tất cả các repository implementation
                // Thường theo quy ước đặt tên kết thúc bằng "Repository"
                var repositoryTypes = assembly.GetExportedTypes()
                    .Where(type => !type.IsAbstract &&
                                   !type.IsInterface &&
                                   type.Name.EndsWith("Repository"))
                    .ToList();

                foreach (var repositoryType in repositoryTypes)
                {
                    // Tìm giao diện mà repository này triển khai
                    var repositoryInterfaces = repositoryType.GetInterfaces()
                        .Where(i => i.Name.StartsWith("I") && i.Name.EndsWith("Repository"))
                        .ToList();

                    foreach (var repositoryInterface in repositoryInterfaces)
                    {
                        // Đăng ký repository theo interface
                        services.Add(new ServiceDescriptor(
                            repositoryInterface,
                            repositoryType,
                            lifetime));

                        Console.WriteLine(
                            $"Registered repository: {repositoryInterface.Name} -> {repositoryType.Name}");
                    }
                }
            }

            return services;
        }
    }
}