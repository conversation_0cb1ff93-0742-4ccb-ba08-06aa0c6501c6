﻿using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterAutoMapper(this IServiceCollection services,
            List<Assembly> assemblies)
        {
            var mapperAssemblies = assemblies.Where(x => x.GetName().Name.EndsWith(".Api") || 
                                                        x.GetName().Name.EndsWith(".Application"));

            services.AddAutoMapper(mapperAssemblies);
            return services;
        }
    }
}