using EvaCheckinScheduleEntity = VNR.Core.Domain.Entities.Evaluation.Eva_CheckinSchedule;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;
public interface IEva_CheckinScheduleService
{
    Task<IEnumerable<EvaCheckinScheduleEntity>> GetAllAsync();
    Task<EvaCheckinScheduleEntity?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(EvaCheckinScheduleEntity entity);
    Task<bool> UpdateAsync(EvaCheckinScheduleEntity entity);
    Task<bool> DeleteAsync(Guid id);
}