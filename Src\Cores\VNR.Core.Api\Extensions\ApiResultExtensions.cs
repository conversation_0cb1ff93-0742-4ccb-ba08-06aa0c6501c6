using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Net;
using System.Threading.Tasks;
using VNR.Core.Models;

namespace VNR.Core.Api.Extensions
{
    public class ApiResultWithSettings<T> : ApiResult<T>
    {
        [JsonIgnore]
        public JsonSerializerSettings SerializerSettings { get; set; }

        [JsonIgnore]
        public HttpStatusCode StatusCode { get; set; }

        public ApiResultWithSettings() { }

        public ApiResultWithSettings(IApiResult<T> apiResult)
        {
            Data = apiResult.Data;
            Status = apiResult.Status;
            Message = apiResult.Message;
            Code = apiResult.Code;
        }

        public async Task ExecuteResultAsync(ActionContext context)
        {
            var response = new ObjectResult(this)
            {
                StatusCode = (int)StatusCode
            };
            await response.ExecuteResultAsync(context);
        }
    }

    public static class ApiResultExtensions
    {
        public static ApiResultWithSettings<T> With<T>(this ApiResult<T> result, Action<ApiResultWithSettings<T>> settingsDelegate)
        {
            var settings = new ApiResultWithSettings<T>(result);
            settingsDelegate?.Invoke(settings);
            return settings;
        }

        public static ApiResultWithSettings<T> WithSerializer<T>(this ApiResult<T> result, JsonSerializerSettings serializerSettings)
        {
            return result.With(settings => settings.SerializerSettings = serializerSettings);
        }

        public static ApiResultWithSettings<T> WithStatusCode<T>(this ApiResult<T> result, HttpStatusCode statusCode)
        {
            return result.With(settings => settings.StatusCode = statusCode);
        }

        public static async Task ExecuteResultAsync<T>(this ApiResult<T> result, ActionContext context)
        {
            var response = new ObjectResult(result)
            {
                StatusCode = (int?)result.Code
            };
            await response.ExecuteResultAsync(context);
        }
    }
}