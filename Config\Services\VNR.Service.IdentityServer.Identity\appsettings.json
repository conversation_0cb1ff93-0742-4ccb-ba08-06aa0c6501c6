{"AppSettingConfiguration": {"reCaptchaPublicKey": "6LcMT-wpAAAAAAGIlnyXqmHvTWUjQ9Is0FNc14it", "reCaptchaPrivateKey": "6LcMT-wpAAAAAO_7hkY153e_JBm5BOk19ATkaA70"}, "CertificateConfiguration": {"UseTemporarySigningKeyForDevelopment": true, "CertificateStoreLocation": "LocalMachine", "CertificateValidOnly": true, "UseSigningCertificateThumbprint": false, "SigningCertificateThumbprint": "", "UseSigningCertificatePfxFile": false, "SigningCertificatePfxFilePath": "", "SigningCertificatePfxFilePassword": "", "UseValidationCertificatePfxFile": false, "ValidationCertificatePfxFilePath": "", "ValidationCertificatePfxFilePassword": "", "UseValidationCertificateThumbprint": false, "ValidationCertificateThumbprint": "", "UseSigningCertificateForAzureKeyVault": false, "UseValidationCertificateForAzureKeyVault": false}, "RegisterConfiguration": {"Enabled": false}, "ExternalProvidersConfiguration": {"UseGitHubProvider": false, "GitHubClientId": "your-github-client-id", "GitHubClientSecret": "your-github-client-secret", "GitHubCallbackPath": "/signin-github", "MicrosoftProvider": {"UseAzureAdProvider": false, "DisplayName": "Sign in Microsoft", "AzureAdClientId": "de2f6e25-4862-42ba-9c3b-538dd8c5b1a0", "AzureAdTenantId": "652aad4b-2343-4ead-8d57-181a1c64b200", "AzureInstance": "https://login.microsoftonline.com/", "AzureAdSecret": "****************************************", "AzureAdCallbackPath": "/signin-microsoft", "AzureDomain": "phucduong.onmicrosoft.com", "Prompt": "select_account"}, "ExternalIdentityProviders": [{"Name": "Google", "DisplayName": "Sign in with Google", "AuthenticationScheme": "Google", "Enabled": false, "Authority": "https://accounts.google.com", "ClientId": "************-ttf11g7r5u6sgku4mbmbvqrb5rd4n7oc.apps.googleusercontent.com", "ClientSecret": "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", "CallbackPath": "/signin-google", "Scopes": ["openid", "profile", "email"], "ClaimMappings": {"sub": "sub", "email": "email", "name": "name"}, "Prompt": "select_account"}]}, "SmtpConfiguration": {"Host": "", "Login": "", "Password": ""}, "SendGridConfiguration": {"ApiKey": "", "SourceEmail": "", "SourceName": ""}, "LoginConfiguration": {"ResolutionPolicy": "Username"}, "AdminConfiguration": {"PageTitle": "VnR IdentityServer", "HomePageLogoUri": "~/images/logo.png", "FaviconUri": "~/favicon.ico", "Theme": null, "CustomThemeCss": null, "IdentityAdminBaseUrl": "https://localhost:44303", "AdministrationRole": "SuperAdmin"}, "CorsSettings": {"AllowedOrigins": ["http://localhost:4200", "http://pehcm14.vnresource.net:8080/tintuc", "http://pehcm14.vnresource.net:8080", "portal4hrm:/Home", "com.hrmeportal", "https://rd-sc.vnrlocal.com", "http://localhost:4000", "http://localhost:5000", "http://localhost:6100", "http://localhost:6200", "https://hn-site.vnrlocal.com:7000", "https://hn-site.vnrlocal.com:7001", "https://hn-site.vnrlocal.com:7004", "https://hn-site.vnrlocal.com:1800", "https://hn-site.vnrlocal.com:1802"], "AllowedMethods": ["GET", "POST", "PUT", "DELETE"], "AllowedHeaders": ["*"], "AllowCredentials": true}, "CspTrustedDomains": ["hn-site.vnrlocal.com", "www.google.com", "www.gstatic.com", "www.gravatar.com", "fonts.googleapis.com", "fonts.gstatic.com", "code.jquery.com", "kendo.cdn.telerik.com", "unpkg.com", "maxcdn.bootstrapcdn.com", "cdn.jsdelivr.net", "cdnjs.cloudflare.com", "wss://localhost:44343", "https://localhost:44343", "http://localhost:50963", "ws://localhost:50963", "http://localhost:*", "https://localhost:*", "ws://localhost:*", "wss://localhost:*"], "CultureConfiguration": {"Cultures": [], "CulturesMapping": [{"Text": "vi", "Value": "VN"}, {"Text": "en", "Value": "EN"}, {"Text": "zh", "Value": "CN"}], "DefaultCulture": null}, "IdentityServerOptions": {"Events": {"RaiseErrorEvents": true, "RaiseInformationEvents": true, "RaiseFailureEvents": true, "RaiseSuccessEvents": true}}, "BasePath": "", "IdentityOptions": {"Password": {"RequiredLength": 8}, "User": {"RequireUniqueEmail": true}, "SignIn": {"RequireConfirmedAccount": false}}, "DataProtectionConfiguration": {"ProtectKeysWithAzureKeyVault": false}, "AzureKeyVaultConfiguration": {"AzureKeyVaultEndpoint": "", "ClientId": "", "ClientSecret": "", "TenantId": "", "GitHubCallbackPath": "", "UseClientCredentials": true, "IdentityServerCertificateName": "", "DataProtectionKeyIdentifier": "", "ReadConfigurationFromKeyVault": false}, "ServiceIdentityConfiguration": {"Clients": [{"ClientId": "api", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "AllowedGrantTypes": ["password", "client_credentials"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true}, {"ClientId": "befb0d83caf64fd6a7a6bac5088eff6b", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["IdentityServerApi"], "AllowedGrantTypes": ["password", "client_credentials"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true}, {"ClientId": "d5641bd831d74ff48157bbfa1a69aba6", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api", "IdentityServerApi"], "AllowedGrantTypes": ["authorization_code", "client_credentials"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequiredPkce": false}, {"ClientId": "hrm10_api", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4004/swagger/hrm.sc.module.shared.api/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code", "client_credentials", "implicit"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4004"]}, {"ClientId": "hrm10_main", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile", "api", "IdentityServerApi"], "RedirectUris": ["https://hn-site.vnrlocal.com:4000/Home/ExternalLoginRedirect/Provider-01"], "FrontChannelLogoutUri": "https://hn-site.vnrlocal.com:4000/Home/ExternalLogoutFromIdp", "PostLogoutRedirectUris": ["https://hn-site.vnrlocal.com:4000/Home/Login"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4000"], "AccessTokenLifetime": 3600}, {"ClientId": "hrm10_portal", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile", "api", "IdentityServerApi"], "RedirectUris": ["https://hn-site.vnrlocal.com:4001/Portal/ExternalLoginRedirect/Provider-01"], "FrontChannelLogoutUri": "https://hn-site.vnrlocal.com:4001/Portal/ExternalLogoutFromIdp", "PostLogoutRedirectUris": ["https://hn-site.vnrlocal.com:4001/Portal/Login"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4001"]}, {"ClientId": "547001045d8e4dabad13f07b5720c953", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile"], "RedirectUris": ["https://hn-site.vnrlocal.com:1801/federation/HRMVnResource/signin"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "RequirePkce": true, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:1801"]}, {"ClientId": "hrm10_portal_local", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile", "api", "IdentityServerApi"], "RedirectUris": ["http://localhost:4200/#/auth/redirect"], "FrontChannelLogoutUri": "http://localhost:4200", "PostLogoutRedirectUris": ["http://localhost:4200/#/auth/login"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "RequireClientSecret": false, "AllowedCorsOrigins": ["http://localhost:4200"]}, {"ClientId": "hrm10_chat_local", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile", "api"], "RedirectUris": ["http://localhost:4200/#/auth/redirect"], "PostLogoutRedirectUris": ["http://localhost:4200/#/auth/login"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "RequireClientSecret": false, "AllowedCorsOrigins": ["http://localhost:4200"], "AccessTokenLifetime": 3600}, {"ClientId": "hrm10_portal_app", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["openid", "profile", "api", "IdentityServerApi"], "RedirectUris": ["portal4hrm:/Home"], "AllowedGrantTypes": ["authorization_code"], "PostLogoutRedirectUris": ["portal4hrm:/Home"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RefreshTokenExpiration": "Sliding", "RequireClientSecret": false}, {"ClientId": "hrm_api_sc_sharedApi", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4004/swagger/hrm.sc.module.shared.api/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4004"]}, {"ClientId": "hrm_api_sc_att", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4004/swagger/hrm.sc.module.att.api/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4004"]}, {"ClientId": "hrm_api_sc_hre", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4004/swagger/hrm.sc.module.hre.api/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4004"]}, {"ClientId": "hrm_api_sc_ins", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4004/swagger/hrm.sc.module.ins.api/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4004"]}, {"ClientId": "hrm_api_hr", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4002/swagger/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4002"]}, {"ClientId": "hrm_api_sys", "ClientSecrets": [{"Value": "K7gNU3sdo+OL0wNhqoVWhr3g6s1xYv72ol/pe/Unols="}], "AllowedScopes": ["api"], "RedirectUris": ["https://hn-site.vnrlocal.com:4003/swagger/oauth2-redirect.html"], "AllowedGrantTypes": ["authorization_code"], "AllowOfflineAccess": true, "AlwaysIncludeUserClaimsInIdToken": true, "RequireClientSecret": false, "AllowedCorsOrigins": ["https://hn-site.vnrlocal.com:4003"]}]}}