using EvaCheckinGoalEntity = VNR.Core.Domain.Entities.Evaluation.Eva_CheckinGoal;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services
{
    public interface IEva_CheckinGoalService
    {
        Task<IEnumerable<EvaCheckinGoalEntity>> GetAllAsync();
        Task<EvaCheckinGoalEntity?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(EvaCheckinGoalEntity entity);
        Task<bool> UpdateAsync(EvaCheckinGoalEntity entity);
        Task<bool> DeleteAsync(Guid id);
    }
}