using EvaCheckinEntity = VNR.Core.Domain.Entities.Evaluation.Eva_Checkin;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Services
{
    public interface IEva_CheckinService
    {
        Task<IEnumerable<EvaCheckinEntity>> GetAllAsync();
        Task<EvaCheckinEntity?> GetByIdAsync(Guid id);
        Task<Guid> CreateAsync(EvaCheckinEntity entity);
        Task<bool> UpdateAsync(EvaCheckinEntity entity);
        Task<bool> DeleteAsync(Guid id);
    }
}