using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_GoalAdjustmentRequest.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Queries;

public class
    ListEvaGoalAdjustmentRequestQueryHandler : QueryListHandler<ListEvaGoalAdjustmentRequestQuery,
    ListEvaGoalAdjustmentRequestDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_GoalAdjustmentRequestService _service;

    public ListEvaGoalAdjustmentRequestQueryHandler(IEva_GoalAdjustmentRequestService service,
        IApplicationContext applicationContext) : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaGoalAdjustmentRequestDto>>> Handle(
        ListEvaGoalAdjustmentRequestQuery request, CancellationToken cancellationToken)
    {
        var result = await _service.GetAllAsync();
        var finalResult = _applicationContext.Mapper.Map<IEnumerable<ListEvaGoalAdjustmentRequestDto>>(result);
        await _applicationContext.LoggingService.LogInformationAsync($"Retrieved {result.Count()} Eva_GoalAdjustmentRequest records");
        return ResultKendoGrid(request.Request, finalResult);
    }
}