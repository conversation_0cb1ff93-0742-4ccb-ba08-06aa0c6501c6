using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Commands;

public class UpdateEvaCheckinGoalCommandHandler : CommandHandler<UpdateEvaCheckinGoalCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinGoalService _service;

    public UpdateEvaCheckinGoalCommandHandler(IEva_CheckinGoalService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaCheckinGoalCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _applicationContext.LoggingService.LogWarningAsync($"Eva_CheckinGoal not found for update: {request.ID}");
            return Result(false);
        }

        _applicationContext.Mapper.Map(request.Request, entity);
        var res = await _service.UpdateAsync(entity);
        return Result(res);
    }
}