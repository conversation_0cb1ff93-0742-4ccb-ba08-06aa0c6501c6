﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using NSwag;
using NSwag.Generation.Processors;
using NSwag.Generation.Processors.Contexts;
using NSwag.Generation.Processors.Security;
using VNR.Core.Configurations.Swagger;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        private static NswagIdentityServerConfiguration _idsConfig { get; set; }
        private static NswagIdentityServerClient _idsClient { get; set; }
        private static List<NswagIdentityServerClient> _idsClients { get; set; }

        public static IServiceCollection RegisterSwagger(this IServiceCollection services, List<Assembly> assemblies, IConfiguration configuration)
        {
            var idsConfig = configuration.GetSection(nameof(NswagIdentityServerConfiguration)).Get<NswagIdentityServerConfiguration>();
            var idsClient = idsConfig?.Clients.Where(p => p.Enabled).FirstOrDefault();

            if (idsConfig != null)
            {
                _idsConfig = idsConfig;
                _idsClients = idsConfig.Clients;
                _idsClient = idsClient;
            }

            var assemblyName = assemblies.FirstOrDefault()?.GetName().Name;

            // Add version 1 document
            services.AddOpenApiDocument(config =>
            {
                config.DocumentName = "v1";
                config.Title = $"{assemblyName} API v1";
                config.Version = "v1";
                config.Description = $"API documentation for {assemblyName} version 1";
                config.OperationProcessors.Add(new OperationSecurityScopeProcessor("Bearer"));
                config.OperationProcessors.Add(new AddRequiredHeaderParameter());
                config.DocumentProcessors.Add(new FilterControllersByAssemblyProcessor(assemblies, "1"));
                config.DocumentProcessors.Add(new AuthDocumentProcessor());
                config.ApiGroupNames = new[] { "1" };

                config.AddSecurity("oauth2", new OpenApiSecurityScheme
                {
                    Type = OpenApiSecuritySchemeType.OAuth2,
                    Description = "OAuth2 Authorization Code Flow",
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = $"{idsClient.Authority}/connect/authorize",
                            TokenUrl = $"{idsClient.Authority}/connect/token",
                            Scopes = _idsClient.Scope.Split(' ').ToDictionary(p => p, p => string.Empty)
                        }
                    }
                });

                if (_idsClients?.Any() == true)
                {
                    foreach (var item in _idsClients)
                    {
                        config.OperationProcessors.Add(new OperationSecurityScopeProcessor(item.Id));
                    }
                }
            }).AddOpenApiDocument(config =>
            {
                config.DocumentName = "v2";
                config.Title = $"{assemblyName} API v2";
                config.Version = "v2";
                config.Description = $"API documentation for {assemblyName} version 2";
                config.OperationProcessors.Add(new OperationSecurityScopeProcessor("Bearer"));
                config.OperationProcessors.Add(new AddRequiredHeaderParameter());
                config.DocumentProcessors.Add(new FilterControllersByAssemblyProcessor(assemblies, "2"));
                config.DocumentProcessors.Add(new AuthDocumentProcessor());
                config.ApiGroupNames = new[] { "2" };

                config.AddSecurity("oauth2", new OpenApiSecurityScheme
                {
                    Type = OpenApiSecuritySchemeType.OAuth2,
                    Description = "OAuth2 Authorization Code Flow",
                    Flows = new OpenApiOAuthFlows
                    {
                        AuthorizationCode = new OpenApiOAuthFlow
                        {
                            AuthorizationUrl = $"{idsClient.Authority}/connect/authorize",
                            TokenUrl = $"{idsClient.Authority}/connect/token",
                            Scopes = idsClient.Scope.Split(' ').ToDictionary(p => p, p => string.Empty)
                        }
                    }
                });

                if (_idsClients?.Any() == true)
                {
                    foreach (var item in _idsClients)
                    {
                        config.OperationProcessors.Add(new OperationSecurityScopeProcessor(item.Id));
                    }
                }
            });

            return services;
        }

        /// <summary>
        /// Swagger Authentication Processor
        /// </summary>
        public class AuthDocumentProcessor : IDocumentProcessor
        {
            public void Process(DocumentProcessorContext context)
            {
                if (_idsClients != null && _idsClients.Any(x => x.Enabled))
                {
                    foreach (var item in _idsClients)
                    {
                        context.Document.SecurityDefinitions.Add(item.Id, new OpenApiSecurityScheme()
                        {
                            Type = OpenApiSecuritySchemeType.OAuth2,
                            Description = item.Description,
                            Flows = new OpenApiOAuthFlows
                            {
                                AuthorizationCode = new OpenApiOAuthFlow
                                {
                                    AuthorizationUrl = $"{item.Authority}/connect/authorize",
                                    TokenUrl = $"{item.Authority}/connect/token",
                                    Scopes = item.Scope.Split(' ').ToDictionary(p => p, p => string.Empty),
                                    RefreshUrl = $"{item.Authority}/connect/token"
                                }
                            }
                        });
                    }
                }
            }
        }

        /// <summary>
        /// Adds Required Headers to Requests
        /// </summary>
        private class AddRequiredHeaderParameter : IOperationProcessor
        {
            public bool Process(OperationProcessorContext context)
            {
                context.OperationDescription.Operation.Parameters.Add(new OpenApiParameter
                {
                    Name = "Accept-Language",
                    Kind = OpenApiParameterKind.Header,
                    Schema = new NJsonSchema.JsonSchema { Type = NJsonSchema.JsonObjectType.String },
                    IsRequired = true,
                    Description = "Languages",
                    Default = "vi"
                });

                return true;
            }
        }

        private class FilterControllersByAssemblyProcessor : IDocumentProcessor
        {
            private readonly List<Assembly> _targetAssemblies;
            private readonly string _targetVersion;

            public FilterControllersByAssemblyProcessor(List<Assembly> assemblies, string targetVersion)
            {
                _targetAssemblies = assemblies;
                _targetVersion = targetVersion;
            }

            public void Process(DocumentProcessorContext context)
            {
                if (!_targetAssemblies.Any())
                {
                    Console.WriteLine("No target assemblies provided!");
                    return;
                }

                // Log initial state
                Console.WriteLine($"Initial document paths count: {context.Document.Paths.Count}");
                Console.WriteLine($"Processing assemblies: {string.Join(", ", _targetAssemblies.Select(a => a.GetName().Name))}");
                Console.WriteLine($"Target version: {_targetVersion}");

                // Collect all controllers from all target assemblies
                var allControllers = new Dictionary<string, Type>(StringComparer.OrdinalIgnoreCase);
                foreach (var assembly in _targetAssemblies)
                {
                    try
                    {
                        var controllers = assembly.GetTypes()
                            .Where(t => typeof(ControllerBase).IsAssignableFrom(t))
                            .ToList();

                        foreach (var controller in controllers)
                        {
                            var controllerName = controller.Name.Replace("Controller", "", StringComparison.OrdinalIgnoreCase);
                            allControllers[controllerName] = controller;
                            Console.WriteLine($"Found controller: {controllerName} in assembly {assembly.GetName().Name}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error processing assembly {assembly.GetName().Name}: {ex.Message}");
                    }
                }

                if (!allControllers.Any())
                {
                    Console.WriteLine("No controllers found in any of the target assemblies!");
                    return;
                }

                Console.WriteLine($"Total controllers found: {allControllers.Count}");

                // Keep only paths that match our controllers and their versions
                var pathsToKeep = context.Document.Paths
                    .Where(path =>
                    {
                        var segments = path.Key.Split('/', StringSplitOptions.RemoveEmptyEntries);
                        if (segments.Length < 2) return false;

                        // Handle both versioned and non-versioned paths
                        string controllerName;
                        string pathVersion;

                        if (segments[0].Equals("api", StringComparison.OrdinalIgnoreCase))
                        {
                            if (segments.Length >= 3 && segments[1].StartsWith("v", StringComparison.OrdinalIgnoreCase))
                            {
                                // Versioned API path: api/v{version}/controller/...
                                controllerName = segments[2];
                                pathVersion = segments[1].Substring(1); // Remove 'v' prefix
                            }
                            else
                            {
                                // Non-versioned API path: api/controller/...
                                controllerName = segments[1];
                                pathVersion = "1"; // Default to v1
                            }
                        }
                        else
                        {
                            return false;
                        }

                        var isMatch = allControllers.ContainsKey(controllerName);

                        if (isMatch)
                        {
                            var controllerType = allControllers[controllerName];
                            var apiVersionAttr = controllerType.GetCustomAttribute<ApiVersionAttribute>();

                            if (apiVersionAttr != null)
                            {
                                // Controller has version attribute, check if it matches target version
                                var isVersionMatch = apiVersionAttr.Versions.Any(v => v.ToString() == _targetVersion);
                                if (isVersionMatch)
                                {
                                    Console.WriteLine($"Keeping versioned path: {path.Key} (matches controller: {controllerName}, version: {_targetVersion})");
                                    return true;
                                }
                            }
                            else if (_targetVersion == "1")
                            {
                                // Controller has no version attribute, only show in v1
                                Console.WriteLine($"Keeping non-versioned path: {path.Key} (matches controller: {controllerName}, default to v1)");
                                return true;
                            }
                        }

                        return false;
                    })
                    .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

                // Replace all paths with our filtered set
                context.Document.Paths.Clear();
                foreach (var path in pathsToKeep)
                {
                    context.Document.Paths.Add(path.Key, path.Value);
                }

                Console.WriteLine($"Final document paths count: {context.Document.Paths.Count}");

                // Clean up unused tags
                var usedTags = context.Document.Paths
                    .SelectMany(path => path.Value.ActualPathItem.Values)
                    .SelectMany(op => op.Tags)
                    .Distinct()
                    .ToList();

                var unusedTags = context.Document.Tags
                    .Where(tag => !usedTags.Contains(tag.Name))
                    .ToList();

                foreach (var tag in unusedTags)
                {
                    context.Document.Tags.Remove(tag);
                }
            }
        }
    }
}