using Microsoft.EntityFrameworkCore;
using VNR.Core.Common.Logging;
using EvaCheckinFeedbackEntity = VNR.Core.Domain.Entities.Evaluation.Eva_CheckinFeedback;
using VNR.Core.Domain.Repository;
using VNR.Infrastructure.BaseRepositories;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinFeedback.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_CheckinFeedback
{
    public class Eva_CheckinFeedbackService : IEva_CheckinFeedbackService
    {
        private readonly IGenericRepository<EvaCheckinFeedbackEntity, Guid> _repository;
        private readonly ILoggingService _logging;
        private readonly IUnitOfWork _unitOfWork;
        public Eva_CheckinFeedbackService(IGenericRepository<EvaCheckinFeedbackEntity, Guid> repository, ILoggingService logging, IUnitOfWork unitOfWork)
            => (_repository, _logging, _unitOfWork) = (repository, logging, unitOfWork);

        public async Task<IEnumerable<EvaCheckinFeedbackEntity>> GetAllAsync()
        {
            await _logging.LogInformationAsync("Get all Eva_CheckinFeedback");
            var query = await _repository.GetAllAsync();
            return await query.ToListAsync();
        }

        public async Task<EvaCheckinFeedbackEntity?> GetByIdAsync(Guid id) => await _repository.GetByIdAsync(id);

        public async Task<Guid> CreateAsync(EvaCheckinFeedbackEntity entity)
        {
            await _repository.AddAsync(entity);
            await _unitOfWork.SaveChangesAsync();
            return entity.Id;
        }

        public async Task<bool> UpdateAsync(EvaCheckinFeedbackEntity entity)
        {
            await _repository.UpdateAsync(entity);
            await _unitOfWork.SaveChangesAsync();
            return true;
        }

        public async Task<bool> DeleteAsync(Guid id)
        {
            var res = await _repository.DeleteAsync(id);
            await _unitOfWork.SaveChangesAsync();
            return res;
        }
    }
}