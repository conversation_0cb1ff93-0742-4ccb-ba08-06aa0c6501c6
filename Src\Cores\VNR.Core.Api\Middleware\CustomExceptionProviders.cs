using System;
using System.Collections.Generic;
using Microsoft.AspNetCore.Http;

namespace VNR.Core.Api.Middleware
{
    /// <summary>
    /// Exception provider that gets exceptions from the HttpContext.Items collection
    /// </summary>
    public class ModernWebApi2ExceptionProvider
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public ModernWebApi2ExceptionProvider(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor ?? throw new ArgumentNullException(nameof(httpContextAccessor));
        }

        /// <summary>
        /// Gets an exception stored in the HttpContext.Items collection under "ExceptionContext" key
        /// </summary>
        public Exception GetException()
        {
            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext?.Items != null && httpContext.Items.TryGetValue("ExceptionContext", out var value))
            {
                return value as Exception;
            }
            return null;
        }
    }
} 