using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Commands;

public class CreateEvaCheckinScheduleCommandHandler : CommandHandler<CreateEvaCheckinScheduleCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinScheduleService _service;

    public CreateEvaCheckinScheduleCommandHandler(IEva_CheckinScheduleService service,
        IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaCheckinScheduleCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Core.Domain.Entities.Evaluation.Eva_CheckinSchedule>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}