﻿using Microsoft.AspNetCore.Http;
using VNR.Core.Models;
using VNR.Core.Models.Base;

namespace VNR.Core.Application.Query
{
    public abstract class QueryListHandler<TQueryList, TResult> : BaseApplicationService, IQueryListHandler<TQueryList, TResult>
        where TResult : BaseDto
        where TQueryList : IQueryList<TResult>
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public QueryListHandler(IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
        { }

        public abstract Task<IApiResult<BaseResponseGridModel<TResult>>> Handle(TQueryList request, CancellationToken cancellationToken);
    }
}