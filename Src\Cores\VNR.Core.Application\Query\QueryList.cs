﻿using Microsoft.AspNetCore.Mvc;
using VNR.Core.Application.Attributes;
using VNR.Core.Models.Base;

namespace VNR.Core.Application.Query
{
    public class QueryList<TResult> : IQueryList<TResult>
        where TResult : BaseDto, new()
    {
        [FromBody]
        [KendoGridRequest]
        public BaseRequestGridModel Request { get; set; }

        public QueryList(BaseRequestGridModel request)
        {
            Request = request ?? new BaseRequestGridModel();
        }
    }

    public class QueryList<TRequest, TResult> : IQueryList<TResult>
        where TRequest : BaseRequestGridModel, new()
        where TResult : BaseDto, new()
    {
        [FromBody]
        [KendoGridRequest]
        public TRequest Request { get; set; }

        public QueryList()
        {
            Request = new TRequest();
        }

        public QueryList(TRequest request)
        {
            Request = request ?? new TRequest();
        }
    }
}