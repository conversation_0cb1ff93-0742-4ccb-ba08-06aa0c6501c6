﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Library</OutputType>
  </PropertyGroup>
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <AssemblyTitle>VNR.Core</AssemblyTitle>
    <Product>VNR.Core</Product>
    <Copyright>Copyright ©  2022</Copyright>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
  </PropertyGroup>
  <ItemGroup>
	<PackageReference Include="AspNetCore.HealthChecks.Elasticsearch" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.Kafka" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.NpgSql" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.Redis" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.SqlServer" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.UI" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.UI.Client" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.UI.Core" Version="8.0.1" />
	<PackageReference Include="AspNetCore.HealthChecks.UI.InMemory.Storage" Version="8.0.1" />
	<PackageReference Include="AutoMapper" Version="14.0.0" />
	<PackageReference Include="Hellang.Middleware.ProblemDetails" Version="6.5.1" />
	<PackageReference Include="MediatR" Version="12.5.0" />
	<PackageReference Include="Microsoft.AspNetCore.Hosting" Version="2.3.0" />
	<PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Abstractions" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.11" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="Microsoft.CSharp" Version="4.7.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.11" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="8.3.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.11" />
    <PackageReference Include="NSwag.AspNetCore" Version="14.3.0" />
    <PackageReference Include="NSwag.Core" Version="14.3.0" />
    <PackageReference Include="NSwag.Generation" Version="14.3.0" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.31" />
    <PackageReference Include="System.Data.DataSetExtensions" Version="4.5.0" />
    <PackageReference Include="Microsoft.AspNetCore.SystemWebAdapters" Version="1.3.0" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\VNR.Core.Configurations\VNR.Core.Configurations.csproj" />
    <ProjectReference Include="..\VNR.Core.Security\VNR.Core.Security.csproj" />
  </ItemGroup>
</Project>