﻿using System;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using VNR.Core.Api.Services;
using VNR.Core.Application.Command;
using VNR.Core.Application.Query;
using VNR.Core.Models.Base;

namespace VNR.Core.Api
{
    /// <summary>
    /// Base CRUD API - One Request
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <typeparam name="TRequest"></typeparam>
    /// <typeparam name="TKey"></typeparam>
    [ApiController]
    public abstract class BaseCrudApiController<TResult, TRequest, TKey> : BaseApiController
        where TResult : BaseDto, new()
        where TRequest : class, new()
        where TKey : IEquatable<TKey>
    {
        public BaseCrudApiController(IApiContext apiContext) : base(apiContext)
        { }

        // GET: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> GetAll([FromBody] BaseRequestGridModel gridRequest)
        {
            var query = new QueryList<TResult>(gridRequest);
            return await HandleRequest(query);
        }

        // GET: api/[controller]/{id}
        [HttpGet("{id}")]
        public virtual async Task<IActionResult> GetById([FromRoute] TKey id)
        {
            var query = new Query<TKey, TResult>(id);
            return await HandleRequest(query);
        }

        // POST: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> Create([FromBody] TRequest dto)
        {
            var command = new CreateCommand<TRequest, TResult>(dto);
            return await HandleRequest(command);
        }

        // PUT: api/[controller]/{id}
        [HttpPut("{id}")]
        public virtual async Task<IActionResult> Update([FromRoute] TKey id, [FromBody] TRequest dto)
        {
            var command = new UpdateCommand<TKey, TRequest, TResult>(id, dto);
            return await HandleRequest(command);
        }

        // DELETE: api/[controller]/{id}
        [HttpDelete("{id}")]
        public virtual async Task<IActionResult> Delete([FromRoute] TKey id)
        {
            var command = new DeleteCommand<TKey, TResult>(id);
            return await HandleRequest(command);
        }
    }

    /// <summary>
    /// Base CRUD API Customize Create & Update Request
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <typeparam name="TCreateRequest"></typeparam>
    /// <typeparam name="TUpdateRequest"></typeparam>
    /// <typeparam name="TKey"></typeparam>
    [ApiController]
    public class BaseCrudApiController<TResult, TQueryList, TCreateRequest, TUpdateRequest, TKey> : BaseApiController
        where TResult : BaseDto, new()
        where TQueryList : BaseRequestGridModel, new()
        where TCreateRequest : class, new()
        where TUpdateRequest : class, new()
        where TKey : IEquatable<TKey>
    {
        protected readonly IMediator _mediator;

        public BaseCrudApiController(IApiContext apiContext) : base(apiContext)
        { }

        // GET: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> List([FromBody] TQueryList request)
        {
            var query = new QueryList<TQueryList, TResult>(request);
            return await HandleRequest(query);
        }

        // GET: api/[controller]/{id}
        [HttpGet("{id}")]
        public virtual async Task<IActionResult> GetById([FromRoute] TKey id)
        {
            var query = new Query<TKey, TResult>(id);
            return await HandleRequest(query);
        }

        // POST: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> Create([FromBody] TCreateRequest dto)
        {
            var command = new CreateCommand<TCreateRequest, TResult>(dto);
            return await HandleRequest(command);
        }

        // PUT: api/[controller]/{id}
        [HttpPut("{id}")]
        public virtual async Task<IActionResult> Update([FromRoute] TKey id, [FromBody] TUpdateRequest dto)
        {
            var command = new UpdateCommand<TKey, TUpdateRequest, TResult>(id, dto);
            return await HandleRequest(command);
        }

        // DELETE: api/[controller]/{id}
        [HttpDelete("{id}")]
        public virtual async Task<IActionResult> Delete([FromRoute] TKey id)
        {
            var command = new DeleteCommand<TKey, TResult>(id);
            return await HandleRequest(command);
        }
    }

    /// <summary>
    /// Base CRUD API Customize Create & Update Request
    /// </summary>
    /// <typeparam name="TResult"></typeparam>
    /// <typeparam name="TCreateOrUpdateRequest"></typeparam>
    /// <typeparam name="TKey"></typeparam>
    [ApiController]
    public class BaseCrudApiController<TResult, TQueryList, TCreateOrUpdateRequest, TKey> : BaseApiController
        where TResult : BaseDto, new()
        where TQueryList : BaseRequestGridModel, new()
        where TCreateOrUpdateRequest : class, new()
        where TKey : IEquatable<TKey>
    {
        protected readonly IMediator _mediator;

        public BaseCrudApiController(IApiContext apiContext) : base(apiContext)
        {
        }

        // GET: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> GetAll([FromBody] TQueryList request)
        {
            var query = new QueryList<TQueryList, TResult>(request);
            return await HandleRequest(query);
        }

        // GET: api/[controller]/{id}
        [HttpGet("{id}")]
        public virtual async Task<IActionResult> GetById([FromRoute] TKey id)
        {
            var query = new Query<TKey, TResult>(id);
            return await HandleRequest(query);
        }

        // POST: api/[controller]
        [HttpPost]
        public virtual async Task<IActionResult> Create([FromBody] TCreateOrUpdateRequest dto)
        {
            var command = new CreateCommand<TCreateOrUpdateRequest, TResult>(dto);
            return await HandleRequest(command);
        }

        // PUT: api/[controller]/{id}
        [HttpPut("{id}")]
        public virtual async Task<IActionResult> Update([FromRoute] TKey id, [FromBody] TCreateOrUpdateRequest dto)
        {
            var command = new UpdateCommand<TKey, TCreateOrUpdateRequest, TResult>(id, dto);
            return await HandleRequest(command);
        }

        // DELETE: api/[controller]/{id}
        [HttpDelete("{id}")]
        public virtual async Task<IActionResult> Delete([FromRoute] TKey id)
        {
            var command = new DeleteCommand<TKey, TResult>(id);
            return await HandleRequest(command);
        }
    }
}