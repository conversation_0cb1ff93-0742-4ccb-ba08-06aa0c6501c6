using VNR.Core.Models.Base;

namespace VNR.Service.Evaluation.Models.Evaluation.Eva_Checkin.ResponseDtos
{
    public class ListEvaCheckinDto : BaseDto
    {
        public Guid? CheckinScheduleId { get; set; }
        public string CheckinName { get; set; }
        public DateTimeOffset? CheckinDate { get; set; }
        public Guid? PeriodId { get; set; }
        public Guid SubmittedById { get; set; }
        public Guid ReviewerId { get; set; }
        public string Status { get; set; }
    }
}
