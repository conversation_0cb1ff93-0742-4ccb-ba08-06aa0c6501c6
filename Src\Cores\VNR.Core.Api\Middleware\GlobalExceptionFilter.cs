﻿using System;
using System.Net;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using VNR.Core.Common.Logging;
using VNR.Core.Exceptions;
using VNR.Core.Models;
using VNR.Core.Models.Enums;

namespace VNR.Core.Api.Middleware
{
    /// <summary>
    /// Global filter exception
    /// </summary>
    public class GlobalExceptionFilterAttribute : ExceptionFilterAttribute
    {
        private readonly ILogger _logger;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILoggingService _loggingService;

        public GlobalExceptionFilterAttribute(IHttpContextAccessor httpContextAccessor, ILogger logger, ILoggingService loggingService)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _loggingService = loggingService;
        }

        public override async void OnException(ExceptionContext context)
        {
            if (context == null)
            {
                throw new ArgumentNullException(nameof(context));
            }

            var request = context.HttpContext.Request;
            var exception = context.Exception;

            if (request == null)
            {
                throw new ArgumentException("request");
            }

            HttpContext httpContext = _httpContextAccessor.HttpContext;
            
            // Store exception in HttpContext.Items for potential later use
            httpContext.Items["ExceptionContext"] = exception;

            // Log the exception using unified logging service
            try
            {
                await _loggingService.LogErrorAsync("Exception caught in GlobalExceptionFilterAttribute", exception, httpContext, httpContext?.TraceIdentifier);
            }
            catch (Exception logEx)
            {
                // Fallback to ILogger if unified logging service fails
                _logger.LogError(exception, "Exception caught in GlobalExceptionFilterAttribute: {RequestPath}", request.Path);
                _logger.LogError(logEx, "Error logging exception with unified logging service");
            }

            // Prepare response
            if (exception.GetType().IsSubclassOf(typeof(ArgumentsValidatorException)))
            {
                ApiResultStatus? statusResponse = null;

                if (exception.GetType() == typeof(RequestArgumentsValidatorException))
                    statusResponse = ApiResultStatus.INVALID_REQUEST_VALIDATOR;
                else if (exception.GetType() == typeof(BusinessArgumentsValidatorException))
                    statusResponse = ApiResultStatus.INVALID_BUSINESS_VALIDATOR;
                else
                    statusResponse = ApiResultStatus.FAIL;

                var result = new ApiResult()
                {
                    Data = ((ArgumentsValidatorException)exception).Parameters,
                    Status = statusResponse.Value
                };

                context.Result = new ObjectResult(result)
                {
                    StatusCode = (int)HttpStatusCode.OK
                };
            }
            else
            {
                var jsonSettings = new JsonSerializerSettings()
                {
                    Formatting = Formatting.Indented
                };

                var resultObject = new ApiResult()
                {
                    Status = ApiResultStatus.EXCEPTION,
                    Data = new
                    {
                        ErrorRoot = "Internal Server Error",
                        ErrorDetails = "An error occurred"
                    },
                    Message = "An error occurred"
                };

                context.Result = new ObjectResult(resultObject)
                {
                    StatusCode = (int)HttpStatusCode.InternalServerError
                };
            }

            // Mark the exception as handled
            context.ExceptionHandled = true;
        }
    }
}