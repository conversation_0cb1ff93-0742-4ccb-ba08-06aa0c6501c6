﻿using Hellang.Middleware.ProblemDetails;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterProblemDetails(this IServiceCollection services)
        {
            services.AddProblemDetails(options =>
            {
                options.IncludeExceptionDetails = (context, ex) =>
                {
                    var env = context.RequestServices.GetRequiredService<IHostEnvironment>();
                    return env.IsDevelopment();
                };
                //options.IncludeExceptionDetails = (context, ex) => false;
            });

            return services;
        }
    }
}