﻿using System;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using VNR.Core.Common.Services;
using VNR.Core.Configurations.Services;
using VNR.Core.Security.Cryptography;

namespace VNR.Core.Extensions
{
    public static partial class StartupExtensionMethods
    {
        public static IServiceCollection RegisterHealthChecks(this IServiceCollection services)
        {
            var provider = services.BuildServiceProvider();

            var configService = provider
                .GetRequiredService<IConfigurationService>();
            var decryptor = provider
                .GetRequiredService<IVnrHrmDecryption>();

            // Lấy cấu hình
            string sqlConnString = configService?.Connections?.HrmConnectionString;
            var redisConfig = configService?.Caching?.RedisCacheConfiguration;

            string nodeUris = string.Empty;
            if (configService != null && configService.Serilog != null && configService.Serilog.WriteTo != null)
            {
                var elasticSearchConfig = configService.Serilog?.WriteTo?.SingleOrDefault(x => x.Name == "Elasticsearch");
                if (elasticSearchConfig != null)
                    nodeUris = elasticSearchConfig.Args["nodeUris"].ToString();
            }

            #region Health Checks by Database Provider

            // Healthchecks
            var healthCheckServices = services
                .AddHealthChecks()
                .AddRedis(decryptor.DecryptConnectionString(redisConfig?.ConnectionString, ',') ?? string.Empty)
                .AddElasticsearch(nodeUris, name: "elasticsearch", timeout: TimeSpan.FromSeconds(5));

            var databaseProvider = provider
             .GetRequiredService<IDatabaseProviderService>();

            var databaseProviderType = databaseProvider.GetCurrentProvider();
            switch (databaseProviderType)
            {
                case Common.Enums.DatabaseProvider.SqlServer:
                    healthCheckServices
                        .AddSqlServer(decryptor.DecryptConnectionString(sqlConnString ?? string.Empty));
                    break;

                case Common.Enums.DatabaseProvider.PostgreSQL:
                    healthCheckServices
                        .AddNpgSql(decryptor.DecryptConnectionString(sqlConnString ?? string.Empty));
                    break;

                default:
                    throw new NotImplementedException($"Health checks not defined for database provider {databaseProviderType}");
            }

            //services.AddHealthChecksUI();

            // Configure the Health Checks UI Client
            //services.AddHealthChecksUIClient(options =>
            //{
            //    options.SetEvaluationTimeInSeconds(10); // set the interval for health check updates
            //    options.MaximumHistoryEntriesPerEndpoint(50); // set the maximum number of entries to keep in the UI
            //});

            //services.AddHealthChecksUI(settings =>
            //    {
            //        configuration.BindUISettings(settings);
            //    })
            //    .AddInMemoryStorage();

            #endregion Health Checks by Database Provider

            return services;
        }
    }
}