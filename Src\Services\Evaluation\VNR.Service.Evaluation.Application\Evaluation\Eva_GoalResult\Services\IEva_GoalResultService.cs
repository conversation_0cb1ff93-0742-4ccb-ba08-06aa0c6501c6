using VNR.Core.Domain.Entities.Evaluation;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResult.Services;

public interface IEva_GoalResultService
{
    Task<IEnumerable<Core.Domain.Entities.Evaluation.Eva_GoalResult>> GetAllAsync();
    Task<Core.Domain.Entities.Evaluation.Eva_GoalResult?> GetByIdAsync(Guid id);
    Task<Guid> CreateAsync(Core.Domain.Entities.Evaluation.Eva_GoalResult entity);
    Task<bool> UpdateAsync(Core.Domain.Entities.Evaluation.Eva_GoalResult entity);
    Task<bool> DeleteAsync(Guid id);
}
