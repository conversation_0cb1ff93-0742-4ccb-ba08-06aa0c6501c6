using Microsoft.EntityFrameworkCore;
using VNR.Core.Common.Logging;
using EvaGoalDetailEntity = VNR.Core.Domain.Entities.Evaluation.Eva_GoalDetail;
using VNR.Core.Domain.Repository;
using VNR.Infrastructure.BaseRepositories;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalDetail.Services;

namespace VNR.Service.Evaluation.Infrastructure.Services.Evaluation.Eva_GoalDetail;

public class Eva_GoalDetailService : IEva_GoalDetailService
{
    private readonly IGenericRepository<EvaGoalDetailEntity, Guid> _repository;
    private readonly ILoggingService _logging;
    private readonly IUnitOfWork _unitOfWork;

    public Eva_GoalDetailService(IGenericRepository<EvaGoalDetailEntity, Guid> repository, ILoggingService logging, IUnitOfWork unitOfWork)
        => (_repository, _logging, _unitOfWork) = (repository, logging, unitOfWork);

    public async Task<IEnumerable<EvaGoalDetailEntity>> GetAllAsync()
    {
        await _logging.LogInformationAsync("Get all Eva_GoalDetail");
        var query = await _repository.GetAllAsync();
        return await query.ToListAsync();
    }

    public async Task<EvaGoalDetailEntity?> GetByIdAsync(Guid id) => await _repository.GetByIdAsync(id);

    public async Task<Guid> CreateAsync(EvaGoalDetailEntity entity)
    {
        await _repository.AddAsync(entity);
        await _unitOfWork.SaveChangesAsync();
        return entity.Id;
    }

    public async Task<bool> UpdateAsync(EvaGoalDetailEntity entity)
    {
        await _repository.UpdateAsync(entity);
        await _unitOfWork.SaveChangesAsync();
        return true;
    }

    public async Task<bool> DeleteAsync(Guid id)
    {
        var res = await _repository.DeleteAsync(id);
        await _unitOfWork.SaveChangesAsync();
        return res;
    }
}
