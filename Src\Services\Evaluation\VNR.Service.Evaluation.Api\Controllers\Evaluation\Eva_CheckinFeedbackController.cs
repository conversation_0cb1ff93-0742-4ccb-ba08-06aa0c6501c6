using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinFeedback.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinFeedback.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Checkin Feedback Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_CheckinFeedback]", Description = "APIs phản hồi checkin")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_CheckinFeedbackController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_CheckinFeedbackController(IApiContext context) : base(context)
    { }

    /// <summary>
    /// Lấy danh sách Eva Checkin Feedbacks
    /// </summary>
    /// <param name="query">Thông tin lọc</param>
    /// <returns>Danh sách Eva Checkin Feedbacks</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaCheckinFeedbackQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Lấy thông tin Eva Checkin Feedback theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaCheckinFeedbackByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Tạo mới Eva Checkin Feedback
    /// </summary>
    /// <param name="command">Thông tin Eva Checkin Feedback</param>
    /// <returns>Id của Eva Checkin Feedback mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaCheckinFeedbackCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Cập nhật Eva Checkin Feedback
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaCheckinFeedbackCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Checkin Feedback
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaCheckinFeedbackCommand command)
        => await HandleRequest(command);
}