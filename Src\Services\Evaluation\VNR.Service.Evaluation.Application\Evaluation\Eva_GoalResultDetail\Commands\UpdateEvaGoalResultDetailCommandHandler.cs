using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalResultDetail.Commands;

public class UpdateEvaGoalResultDetailCommandHandler : CommandHandler<UpdateEvaGoalResultDetailCommand, bool>
{
    private readonly IApplicationContext _context;
    private readonly IEva_GoalResultDetailService _service;

    public UpdateEvaGoalResultDetailCommandHandler(IEva_GoalResultDetailService service, IApplicationContext context)
        : base(context.Accessor)
    {
        (_context, _service) = (context, service);
    }

    public override async Task<IApiResult<bool>> Handle(UpdateEvaGoalResultDetailCommand request,
        CancellationToken cancellationToken)
    {
        var entity = await _service.GetByIdAsync(request.ID);
        if (entity == null)
        {
            await _context.LoggingService.LogWarningAsync($"Eva_GoalResultDetail not found for update: {request.ID}");
            return Result(false);
        }

        _context.Mapper.Map(request.Request, entity);
        var result = await _service.UpdateAsync(entity);
        return Result(result);
    }
}