using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_GoalAdjustmentRequest.Commands;

public class CreateEvaGoalAdjustmentRequestCommandHandler : CommandHandler<CreateEvaGoalAdjustmentRequestCommand, Guid>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_GoalAdjustmentRequestService _service;

    public CreateEvaGoalAdjustmentRequestCommandHandler(IEva_GoalAdjustmentRequestService service,
        IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<Guid>> Handle(CreateEvaGoalAdjustmentRequestCommand request,
        CancellationToken cancellationToken)
    {
        var entity = _applicationContext.Mapper.Map<Core.Domain.Entities.Evaluation.Eva_GoalAdjustmentRequest>(request.Request);
        var id = await _service.CreateAsync(entity);
        return Result(id);
    }
}