using System;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.Extensions.DependencyInjection;

namespace VNR.Core.Services
{
    public interface IModelInitializationService
    {
        Task<IModel> GetModelAsync();

        IModel GetModel();
    }

    public class ModelInitializationService : IModelInitializationService
    {
        private readonly Func<IModel> _modelFactory;
        private readonly Lazy<Task<IModel>> _lazyModelTask;
        private IModel _cachedModel;

        public ModelInitializationService(Func<IModel> modelFactory)
        {
            _modelFactory = modelFactory ?? throw new ArgumentNullException(nameof(modelFactory));
            _lazyModelTask = new Lazy<Task<IModel>>(() => Task.Run(() => _modelFactory()));
        }

        public async Task<IModel> GetModelAsync() => await _lazyModelTask.Value;

        public IModel GetModel() => _cachedModel ??= _modelFactory();
    }

    public static class ModelInitializationServiceExtensions
    {
        public static IServiceCollection AddModelInitialization(this IServiceCollection services, Func<IModel> modelFactory)
        {
            services.AddSingleton<IModelInitializationService>(sp => new ModelInitializationService(modelFactory));
            return services;
        }
    }
}