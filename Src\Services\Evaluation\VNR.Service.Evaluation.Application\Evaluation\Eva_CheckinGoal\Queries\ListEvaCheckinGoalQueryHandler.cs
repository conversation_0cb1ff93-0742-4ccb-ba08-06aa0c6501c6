using VNR.Core.Application.Query;
using VNR.Core.Models;
using VNR.Core.Models.Base;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Services;
using VNR.Service.Evaluation.Models.Evaluation.Eva_CheckinGoal.ResponseDtos;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinGoal.Queries;

public class ListEvaCheckinGoalQueryHandler : QueryListHandler<ListEvaCheckinGoalQuery, ListEvaCheckinGoalDto>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinGoalService _service;

    public ListEvaCheckinGoalQueryHandler(IEva_CheckinGoalService service, IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<BaseResponseGridModel<ListEvaCheckinGoalDto>>> Handle(
        ListEvaCheckinGoalQuery request, CancellationToken cancellationToken)
    {
        var result = await _service.GetAllAsync();
        var finalResult = _applicationContext.Mapper.Map<IEnumerable<ListEvaCheckinGoalDto>>(result);
        await _applicationContext.LoggingService.LogInformationAsync($"Retrieved {result.Count()} Eva_CheckinGoal records");
        return ResultKendoGrid(request.Request, finalResult);
    }
}