﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_Checkin.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Checkin Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_Checkin]", Description = "APIs checkin đánh giá")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_CheckinController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_CheckinController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// Lấy danh sách Eva Checkins
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Checkins</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaCheckinQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Checkin
    /// </summary>
    /// <param name="command">Thông tin Eva Checkin</param>
    /// <returns>Id của Eva Checkin mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaCheckinCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Checkin theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaCheckinByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Checkin
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaCheckinCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Checkin
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaCheckinCommand command)
        => await HandleRequest(command);
}