﻿using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationModels;

namespace VNR.Core.Api.Middleware
{
    public class DefaultRouteConvention : IControllerModelConvention
    {
        private readonly HashSet<string> _targetAssemblyNames;

        public DefaultRouteConvention(IEnumerable<string> targetAssemblyNames)
        {
            _targetAssemblyNames = new HashSet<string>(targetAssemblyNames);
        }

        public void Apply(ControllerModel controller)
        {
            // Get the assembly name of the controller
            string assemblyName = controller?.ControllerType?.Assembly?.GetName().Name;
            var validProjectLibAssembly = _targetAssemblyNames?.FirstOrDefault(x => x == assemblyName);
            
            // Apply route only if the controller belongs to one of the target libraries
            if (validProjectLibAssembly != null)
            {
                bool hasRoute = controller.Attributes.OfType<RouteAttribute>().Any();

                if (!hasRoute)
                {
                    // Apply default route pattern for the target assemblies
                    controller.Selectors.Add(new SelectorModel
                    {
                        AttributeRouteModel = new AttributeRouteModel(new RouteAttribute($"api/[controller]/[action]"))
                    });
                }
            }
        }
    }
}