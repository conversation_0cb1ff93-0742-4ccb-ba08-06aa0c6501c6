{"Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.Elasticsearch", "Serilog.Sinks.File"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning", "Microsoft.AspNetCore": "Warning"}}, "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId", "WithEnvironmentName", "WithCorrelationId", "WithClientIp", "WithUserAgent"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {SourceContext} {Message:lj}{NewLine}{Exception}"}}, {"Name": "Elasticsearch", "Args": {"nodeUris": "http://************:9200", "indexFormat": "vnr-logs-{0:yyyy.MM.dd}", "autoRegisterTemplate": true}}, {"Name": "Seq", "Args": {"serverUrl": "http://************:5341", "apiKey": "0D9sCpRUbwuRRyN5vXQ5"}}, {"Name": "File", "Args": {"path": "../../../../Logs/VNR.Service.Sample.Api/log-.json", "rollingInterval": "Day", "retainedFileCountLimit": 31, "shared": true, "flushToDiskInterval": "00:00:01", "fileSizeLimitBytes": 10485760, "rollOnFileSizeLimit": true, "formatter": "Serilog.Formatting.Json.Json<PERSON>, Ser<PERSON>g"}}], "FileSink": {"Enabled": true, "DefaultPath": "../../../../Logs/{Application}/{LogType}/{LogType}-.json", "RollingInterval": "Day", "RetainedFileCountLimit": 31, "Shared": true, "FlushToDiskInterval": "00:00:01", "FileSizeLimitBytes": 10485760, "RollOnFileSizeLimit": true, "Formatter": "Json", "LogTypes": {"Access": {"Enabled": true, "MinimumLevel": "Information", "Path": "../../../../Logs/{Application}/Access/access-.json"}, "Error": {"Enabled": true, "MinimumLevel": "Error", "Path": "../../../../Logs/{Application}/Error/error-.json"}, "Trace": {"Enabled": true, "MinimumLevel": "Debug", "Path": "../../../../Logs/{Application}/Trace/trace-.json"}, "System": {"Enabled": true, "MinimumLevel": "Information", "Path": "../../../../Logs/{Application}/System/system-.json"}}}}}