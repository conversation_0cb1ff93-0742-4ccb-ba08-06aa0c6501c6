using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using VNR.Core.Common.Logging;

namespace VNR.Core.Api.Middleware;

public class AccessLogMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILoggingService _loggingService;

    public AccessLogMiddleware(
        RequestDelegate next,
        ILoggingService loggingService)
    {
        _next = next;
        _loggingService = loggingService;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var stopwatch = Stopwatch.StartNew();
        var startTime = DateTime.UtcNow;

        try
        {
            // Call the next middleware in the pipeline
            await _next(context);
        }
        finally
        {
            stopwatch.Stop();
            var endTime = DateTime.UtcNow;
            var duration = stopwatch.Elapsed;

            // Log access information using unified logging service
            try
            {
                await _loggingService.LogAccessAsync(context, startTime, endTime, duration);
            }
            catch (Exception ex)
            {
                // Fallback logging if logging service fails
                Console.WriteLine($"Error logging access information: {ex.Message}");
            }
        }
    }
}