using VNR.Core.Application.Command;
using VNR.Core.Models;
using VNR.Infrastructure.BaseRepositories.Services.ApplicationContext;
using VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Services;

namespace VNR.Service.Evaluation.Application.Evaluation.Eva_CheckinSchedule.Commands;

public class DeleteEvaCheckinScheduleCommandHandler : CommandHandler<DeleteEvaCheckinScheduleCommand, bool>
{
    private readonly IApplicationContext _applicationContext;
    private readonly IEva_CheckinScheduleService _service;

    public DeleteEvaCheckinScheduleCommandHandler(IEva_CheckinScheduleService service,
        IApplicationContext applicationContext)
        : base(applicationContext.Accessor)
    {
        (_applicationContext, _service) = (applicationContext, service);
    }

    public override async Task<IApiResult<bool>> Handle(DeleteEvaCheckinScheduleCommand request,
        CancellationToken cancellationToken)
    {
        var res = await _service.DeleteAsync(request.ID);
        return Result(res);
    }
}