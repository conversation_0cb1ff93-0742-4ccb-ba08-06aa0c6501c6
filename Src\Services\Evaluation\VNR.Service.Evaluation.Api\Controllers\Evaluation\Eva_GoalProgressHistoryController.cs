﻿using Microsoft.AspNetCore.Mvc;
using NSwag.Annotations;
using VNR.Core.Api;
using VNR.Core.Api.Services;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Commands;
using VNR.Service.Evaluation.Application.Evaluation.Eva_GoalProgressHistory.Queries;

namespace VNR.Service.Evaluation.Api.Controllers.Evaluation;

/// <summary>
/// Eva Goal Progress History Controller
/// </summary>
[OpenApiTag("[Evaluation][Eva_GoalProgressHistory]", Description = "APIs lịch sử tiến độ mục tiêu")]
[ApiVersion("1")]
[Route("api/v{version:apiVersion}/[controller]")]
public class Eva_GoalProgressHistoryController : BaseApiController
{
    /// <summary>
    /// Constructor
    /// </summary>
    public Eva_GoalProgressHistoryController(IApiContext apiContext) : base(apiContext)
    { }

    /// <summary>
    /// L<PERSON>y danh sách Eva Goal Progress Histories
    /// </summary>
    /// <param name="request">Thông tin lọc</param>
    /// <returns>Danh sách Eva Goal Progress Histories</returns>
    [HttpGet]
    public async Task<IActionResult> List(ListEvaGoalProgressHistoryQuery request)
        => await HandleRequest(request);

    /// <summary>
    /// Tạo mới Eva Goal Progress History
    /// </summary>
    /// <param name="command">Thông tin Eva Goal Progress History</param>
    /// <returns>Id của Eva Goal Progress History mới</returns>
    [HttpPost]
    public async Task<IActionResult> Create(CreateEvaGoalProgressHistoryCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Lấy thông tin Eva Goal Progress History theo Id
    /// </summary>
    [HttpGet("{ID:guid}")]
    public async Task<IActionResult> GetById(GetEvaGoalProgressHistoryByIdQuery query)
        => await HandleRequest(query);

    /// <summary>
    /// Cập nhật Eva Goal Progress History
    /// </summary>
    [HttpPut("{ID:guid}")]
    public async Task<IActionResult> Update(UpdateEvaGoalProgressHistoryCommand command)
        => await HandleRequest(command);

    /// <summary>
    /// Xóa Eva Goal Progress History
    /// </summary>
    [HttpDelete("{ID:guid}")]
    public async Task<IActionResult> Delete(DeleteEvaGoalProgressHistoryCommand command)
        => await HandleRequest(command);
}